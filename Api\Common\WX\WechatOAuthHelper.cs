using Common.Https;
using Common.Log4Net;
using Newtonsoft.Json;
using System.Web;

namespace Common.WX
{
    /// <summary>
    /// 微信OAuth2.0帮助类日志记录器
    /// </summary>
    internal class WechatOAuthHelperLogger
    {
    }

    /// <summary>
    /// 微信OAuth2.0帮助类
    /// </summary>
    public static class WechatOAuthHelper
    {
        /// <summary>
        /// 微信OAuth2.0授权URL
        /// </summary>
        private const string OAUTH_AUTHORIZE_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";

        /// <summary>
        /// 微信OAuth2.0获取access_token的URL
        /// </summary>
        private const string OAUTH_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/sns/oauth2/access_token";

        /// <summary>
        /// 微信OAuth2.0获取用户信息的URL
        /// </summary>
        private const string OAUTH_USERINFO_URL = "https://api.weixin.qq.com/sns/userinfo";

        /// <summary>
        /// 微信基础access_token获取URL
        /// </summary>
        private const string BASIC_ACCESS_TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

        /// <summary>
        /// 生成微信OAuth2.0授权URL
        /// </summary>
        /// <param name="appId">微信AppID</param>
        /// <param name="redirectUri">授权后重定向的回调链接地址</param>
        /// <param name="scope">应用授权作用域</param>
        /// <param name="state">重定向后会带上state参数</param>
        /// <returns>授权URL</returns>
        public static string GenerateOAuthUrl(string appId, string redirectUri, string scope = "snsapi_userinfo", string? state = null)
        {
            LoggerHelper<WechatOAuthHelperLogger>.Info($"生成微信OAuth授权URL - AppId: {appId}, RedirectUri: {redirectUri}, Scope: {scope}, State: {state}");

            var encodedRedirectUri = HttpUtility.UrlEncode(redirectUri);
            var url = $"{OAUTH_AUTHORIZE_URL}?appid={appId}&redirect_uri={encodedRedirectUri}&response_type=code&scope={scope}";

            if (!string.IsNullOrEmpty(state))
            {
                url += $"&state={HttpUtility.UrlEncode(state)}";
            }

            url += "#wechat_redirect";

            LoggerHelper<WechatOAuthHelperLogger>.Info($"成功生成微信OAuth授权URL: {url}");
            return url;
        }

        /// <summary>
        /// 通过code换取网页授权access_token
        /// </summary>
        /// <param name="appId">微信AppID</param>
        /// <param name="appSecret">微信AppSecret</param>
        /// <param name="code">授权码</param>
        /// <returns>access_token信息</returns>
        public static async Task<WechatAccessTokenResponseDto?> GetOAuthAccessTokenAsync(string appId, string appSecret, string code)
        {
            var url = $"{OAUTH_ACCESS_TOKEN_URL}?appid={appId}&secret={appSecret}&code={code}&grant_type=authorization_code";
            LoggerHelper<WechatOAuthHelperLogger>.Info($"开始获取微信OAuth access_token - AppId: {appId}, Code: {code}");

            try
            {
                var response = await HttpHelper.GetAsync(url);
                if (string.IsNullOrEmpty(response))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error("微信OAuth access_token响应为空");
                    return null;
                }

                LoggerHelper<WechatOAuthHelperLogger>.Info($"微信OAuth access_token响应: {response}");

                // 检查是否有错误
                if (response.Contains("errcode"))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error($"微信OAuth获取access_token失败: {response}");

                    // 解析错误信息
                    try
                    {
                        var errorResponse = JsonConvert.DeserializeObject<dynamic>(response);
                        var errcode = errorResponse?.errcode;
                        var errmsg = errorResponse?.errmsg;
                        LoggerHelper<WechatOAuthHelperLogger>.Error($"微信API错误详情 - 错误码: {errcode}, 错误信息: {errmsg}");

                        // 记录常见错误的解决建议
                        switch (errcode?.ToString())
                        {
                            case "40029":
                                LoggerHelper<WechatOAuthHelperLogger>.Error("错误原因: code无效，可能已过期或已被使用");
                                break;
                            case "40013":
                                LoggerHelper<WechatOAuthHelperLogger>.Error("错误原因: appid无效，请检查微信配置");
                                break;
                            case "40125":
                                LoggerHelper<WechatOAuthHelperLogger>.Error("错误原因: appsecret无效，请检查微信配置");
                                break;
                            case "40163":
                                LoggerHelper<WechatOAuthHelperLogger>.Error("错误原因: code已被使用，请重新授权");
                                break;
                        }
                    }
                    catch (Exception parseEx)
                    {
                        LoggerHelper<WechatOAuthHelperLogger>.Error($"解析微信错误响应失败: {parseEx.Message}");
                    }

                    return null;
                }

                var tokenResponse = JsonConvert.DeserializeObject<WechatAccessTokenResponseDto>(response);

                LoggerHelper<WechatOAuthHelperLogger>.Info($"JSON反序列化结果 - AccessToken长度: {tokenResponse?.AccessToken?.Length ?? 0}位, OpenId: {tokenResponse?.OpenId}, UnionId: {tokenResponse?.UnionId}, ExpiresIn: {tokenResponse?.ExpiresIn}");
                LoggerHelper<WechatOAuthHelperLogger>.Info($"成功获取微信OAuth access_token - OpenId: {tokenResponse?.OpenId}, UnionId: {tokenResponse?.UnionId}, ExpiresIn: {tokenResponse?.ExpiresIn}");
                return tokenResponse;
            }
            catch (Exception ex)
            {
                LoggerHelper<WechatOAuthHelperLogger>.Error($"获取微信OAuth access_token异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 拉取用户信息(需scope为 snsapi_userinfo)
        /// </summary>
        /// <param name="accessToken">网页授权接口调用凭证</param>
        /// <param name="openId">用户的唯一标识</param>
        /// <returns>用户信息</returns>
        public static async Task<WechatUserInfoDto?> GetUserInfoAsync(string accessToken, string openId)
        {
            var url = $"{OAUTH_USERINFO_URL}?access_token={accessToken}&openid={openId}&lang=zh_CN";
            LoggerHelper<WechatOAuthHelperLogger>.Info($"开始获取微信用户信息 - OpenId: {openId}, AccessToken长度: {accessToken?.Length ?? 0}位");

            try
            {
                var response = await HttpHelper.GetAsync(url);
                if (string.IsNullOrEmpty(response))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error("微信用户信息响应为空");
                    return null;
                }

                LoggerHelper<WechatOAuthHelperLogger>.Info($"微信用户信息响应: {response}");

                // 检查是否有错误
                if (response.Contains("errcode"))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error($"微信获取用户信息失败: {response}");
                    return null;
                }

                var userInfo = JsonConvert.DeserializeObject<WechatUserInfoDto>(response);
                LoggerHelper<WechatOAuthHelperLogger>.Info($"成功获取微信用户信息 - 昵称: {userInfo?.Nickname}, UnionId: {userInfo?.UnionId}, 性别: {userInfo?.Sex}, 城市: {userInfo?.City}");
                return userInfo;
            }
            catch (Exception ex)
            {
                LoggerHelper<WechatOAuthHelperLogger>.Error($"获取微信用户信息异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 获取微信基础access_token（用于调用其他微信API）
        /// </summary>
        /// <param name="appId">微信AppID</param>
        /// <param name="appSecret">微信AppSecret</param>
        /// <returns>基础access_token</returns>
        public static async Task<WechatBasicAccessTokenDto?> GetBasicAccessTokenAsync(string appId, string appSecret)
        {
            var url = $"{BASIC_ACCESS_TOKEN_URL}?grant_type=client_credential&appid={appId}&secret={appSecret}";
            LoggerHelper<WechatOAuthHelperLogger>.Info($"开始获取微信基础access_token - AppId: {appId}");

            try
            {
                var response = await HttpHelper.GetAsync(url);
                if (string.IsNullOrEmpty(response))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error("微信基础access_token响应为空");
                    return null;
                }

                LoggerHelper<WechatOAuthHelperLogger>.Info($"微信基础access_token响应: {response}");

                // 检查是否有错误
                if (response.Contains("errcode"))
                {
                    LoggerHelper<WechatOAuthHelperLogger>.Error($"微信获取基础access_token失败: {response}");
                    return null;
                }

                var tokenResponse = JsonConvert.DeserializeObject<WechatBasicAccessTokenDto>(response);
                LoggerHelper<WechatOAuthHelperLogger>.Info($"成功获取微信基础access_token - ExpiresIn: {tokenResponse?.ExpiresIn}秒");
                return tokenResponse;
            }
            catch (Exception ex)
            {
                LoggerHelper<WechatOAuthHelperLogger>.Error($"获取微信基础access_token异常: {ex.Message}", ex);
                return null;
            }
        }

        /// <summary>
        /// 检验授权凭证（access_token）是否有效
        /// </summary>
        /// <param name="accessToken">网页授权接口调用凭证</param>
        /// <param name="openId">用户的唯一标识</param>
        /// <returns>是否有效</returns>
        public static async Task<bool> ValidateAccessTokenAsync(string accessToken, string openId)
        {
            var url = $"https://api.weixin.qq.com/sns/auth?access_token={accessToken}&openid={openId}";

            try
            {
                var response = await HttpHelper.GetAsync(url);
                if (string.IsNullOrEmpty(response))
                    return false;

                var result = JsonConvert.DeserializeObject<dynamic>(response);
                return result?.errcode == 0;
            }
            catch
            {
                return false;
            }
        }
    }
}
