
＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 14:59:46,579                                   
【执行时间】[80]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803145945 访问批次 [测试批次] [测试视频] test_video_001                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 15:01:28,715                                   
【执行时间】[102216]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803150126 访问批次 [测试批次] [测试视频] test_video_full                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 15:23:44,309                                   
【执行时间】[100]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803152341 完播视频 [测试批次] [测试视频] test_video_full                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 15:23:44,309                                   
【执行时间】[100]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803152341 访问批次 [测试批次] [测试视频] test_video_full                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 15:54:52,960                                   
【执行时间】[99]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803155450 访问批次 [测试批次] [测试视频] test_video_full                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 15:54:52,960                                   
【执行时间】[99]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 d7c2bee7dec94baba49b72d9039b4606 完播批次 8                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 16:07:39,810                                   
【执行时间】[86]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803160737 访问批次 [测试批次] [测试视频] test_video_full                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 16:07:39,810                                   
【执行时间】[86]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 3db40520dbae447cafbc3cbfb1ed49b9 完播批次 8                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 16:07:40,799                                   
【执行时间】[1074]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：用户 测试用户_0803160737 完成答题，正确率 80.0%                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 16:07:41,852                                   
【执行时间】[2128]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】模块：UserBatchRecord，操作：向用户 测试用户_0803160737 发放红包 6.66 元                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 17:14:19,149                                   
【执行时间】[99]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】生成微信OAuth授权URL - AppId: wx02bacea38f7f3ab5, RedirectUri: https://example.com/callback, Scope: snsapi_userinfo, State:                                    
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 17:14:19,163                                   
【执行时间】[113]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】成功生成微信OAuth授权URL: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx02bacea38f7f3ab5&redirect_uri=https%3a%2f%2fexample.com%2fcallback&response_type=code&scope=snsapi_userinfo#wechat_redirect                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:02:09,489                                   
【执行时间】[38170]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== WechatController.CheckConfig 开始 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:02:09,497                                   
【执行时间】[38179]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】微信配置检查 - AppId: wx02bacea38f7f3ab5, AppSecret: 已设置(32位)                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:04:41,742                                   
【执行时间】[190424]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】收到获取微信OAuth授权URL请求，RedirectUri: https://test.com, Scope: snsapi_userinfo, State: test123, InviterId:                                    
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:04:41,745                                   
【执行时间】[190427]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】生成微信OAuth授权URL - AppId: wx02bacea38f7f3ab5, RedirectUri: https://test.com, Scope: snsapi_userinfo, State: test123                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:04:41,745                                   
【执行时间】[190427]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】成功生成微信OAuth授权URL: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx02bacea38f7f3ab5&redirect_uri=https%3a%2f%2ftest.com&response_type=code&scope=snsapi_userinfo&state=test123#wechat_redirect                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:04:41,745                                   
【执行时间】[190427]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】成功生成微信OAuth授权URL: https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx02bacea38f7f3ab5&redirect_uri=https%3a%2f%2ftest.com&response_type=code&scope=snsapi_userinfo&state=test123#wechat_redirect                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:08:13,406                                   
【执行时间】[46117]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== WechatController.CheckConfig 开始 - INFO测试 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:08:13,407                                   
【执行时间】[46117]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】微信配置检查 - INFO测试 - AppId: wx02bacea38f7f3ab5, AppSecret: 已设置(32位)                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:25:15,833                                   
【执行时间】[93171]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== WechatController.CheckConfig 开始 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:25:15,842                                   
【执行时间】[93180]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】微信配置检查 - AppId: wx02bacea38f7f3ab5, AppSecret: 已设置(32位)                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:25:15,842                                   
【执行时间】[93181]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== WechatController.CheckConfig 完成 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:26:43,477                                   
【执行时间】[180815]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== 微信OAuth回调接口开始 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:26:43,477                                   
【执行时间】[180815]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】接收参数 - Code: invalid_code, State: test                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:26:43,477                                   
【执行时间】[180816]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】开始调用WechatOAuthService处理回调                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:26:43,545                                   
【执行时间】[180884]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】开始获取微信OAuth access_token - AppId: wx02bacea38f7f3ab5, Code: invalid_code                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】INFO                                    
【记录时间】2025-08-03 21:26:43,962                                   
【执行时间】[181301]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】微信OAuth access_token响应: {"errcode":40029,"errmsg":"invalid code, rid: 688f6393-27f91c4f-5ac2f485"}                                   
【错误详情】
