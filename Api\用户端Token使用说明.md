# 用户端Token系统使用说明

## 概述

本系统实现了独立的用户端Token体系，专门用于微信用户的业务功能（观看视频、答题、红包等），与管理系统Token完全分离。

## 系统架构

### 1. 双Token设计
- **管理系统Token**：用于API身份验证，短期有效（2小时）
- **用户端Token**：用于业务功能，长期有效（7天）

### 2. 独立验证体系
- 不同的JWT密钥和算法
- 独立的权限验证中间件
- 完全隔离的用户信息管理

## 核心组件

### 1. UserJWTHelper (`BLL/Common/UserJWTHelper.cs`)
```csharp
// 生成用户端Token
string userToken = UserJWTHelper.GenerateUserToken(user);

// 验证Token
var (isValid, principal, errorMessage) = UserJWTHelper.ValidateUserToken(token);

// 获取用户信息
string userId = UserJWTHelper.GetUserIdFromToken(token);
int? auditStatus = UserJWTHelper.GetAuditStatusFromToken(token);
string inviterId = UserJWTHelper.GetInviterIdFromToken(token);
```

### 2. UserTokenAuthMiddleware (`ServiceVideoSharing/Middleware/UserTokenAuthMiddleware.cs`)
- 自动验证用户端API的Token
- 检查用户审核状态
- 将用户信息注入HttpContext

### 3. HttpContext扩展方法
```csharp
// 在Controller中获取用户信息
string userId = HttpContext.GetCurrentUserId();
int? auditStatus = HttpContext.GetCurrentUserAuditStatus();
string inviterId = HttpContext.GetCurrentUserInviterId();
```

## API使用方式

### 1. 微信OAuth登录
```http
POST /api/wechat/callback
```

**响应示例：**
```json
{
  "success": true,
  "data": {
    "accessToken": "管理系统JWT Token",
    "userToken": "用户端业务Token",
    "userTokenExpiryDays": 7,
    "userInfo": {
      "id": "user_123",
      "auditStatus": 1,
      "inviterId": "EMP001"
    },
    "isNewUser": true,
    "auditStatus": 1,
    "inviterId": "EMP001"
  },
  "message": "新用户注册并登录成功"
}
```

### 2. 用户端API调用

**请求头设置：**
```http
User-Token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

或者：
```http
Authorization: UserBearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

**示例API：**
```http
GET /api/user/video/list
User-Token: {userToken}
```

## 用户端API路径

系统会自动对以下路径进行用户端Token验证：
- `/api/user/video/*` - 视频相关
- `/api/user/progress/*` - 观看进度
- `/api/user/quiz/*` - 答题相关
- `/api/user/redpacket/*` - 红包相关
- `/api/user/profile/*` - 用户信息

## 审核状态处理

### 审核状态说明
- `0` - 待审核：用户注册后等待员工审核
- `1` - 已通过：可以正常使用所有功能
- `2` - 已拒绝：账户被拒绝，无法使用功能

### 自动审核配置
- 通过`SystemConfig`表的`AUTO_AUDIT_ENABLED`配置控制
- 开启：新用户直接设为"已通过"
- 关闭：新用户设为"待审核"

## 邀请人绑定机制

### 1. 生成带邀请人的授权链接
```http
GET /api/wechat/authorize?redirectUri=xxx&scope=snsapi_userinfo&state=xxx&inviterId=EMP001
```

### 2. State参数结构
```json
{
  "timestamp": 1672531200,
  "random": "abc12345",
  "returnUrl": "原始state参数",
  "inviterId": "EMP001"
}
```

### 3. 自动绑定逻辑
- 新用户：自动绑定到邀请人
- 已有用户：跳过绑定，正常登录

## 中间件配置

在`Program.cs`或`Startup.cs`中添加：
```csharp
// 添加用户Token验证中间件（在路由之前）
app.UseUserTokenAuth();
```

## 错误处理

### Token验证失败响应
```json
{
  "success": false,
  "message": "Token已过期",
  "code": 401,
  "timestamp": "2025-08-02 16:52:54"
}
```

### 审核状态限制响应
```json
{
  "success": false,
  "message": "账户待审核，暂无法使用此功能",
  "code": 401,
  "timestamp": "2025-08-02 16:52:54"
}
```

## 安全特性

1. **独立密钥**：用户端Token使用独立的JWT密钥
2. **类型验证**：Token中包含类型标识，防止混用
3. **时间验证**：严格的过期时间检查
4. **审核控制**：基于审核状态的功能访问控制
5. **日志记录**：完整的验证和访问日志

## 扩展说明

### 添加新的用户端API
1. 创建Controller，路径以`/api/user/`开头
2. 在Controller中使用`HttpContext.GetCurrentUserId()`等方法获取用户信息
3. 根据业务需求检查审核状态和权限

### 修改验证路径
在`UserTokenAuthMiddleware.IsUserApiPath()`方法中修改需要验证的路径列表。

### 自定义Token有效期
在`UserJWTHelper`中修改`UserTokenExpiry`常量。

## 测试建议

1. 使用Postman等工具测试API
2. 验证不同审核状态下的功能访问
3. 测试Token过期和刷新机制
4. 验证邀请人绑定逻辑
