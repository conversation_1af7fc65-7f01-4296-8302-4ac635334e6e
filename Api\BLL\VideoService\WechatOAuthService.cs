using BLL.Common;
using Common;
using Common.Exceptions;
using Common.JWT;
using Common.WX;
using DAL.VideoDAL;
using Entity.Dto.VideoDto;
using Entity.Entitys.VideoEntity;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using WxAccessTokenDto = Common.WX.WechatAccessTokenResponseDto;

namespace BLL.VideoService
{
    /// <summary>
    /// 微信OAuth2.0服务
    /// </summary>
    public class WechatOAuthService(
        UserDAL userDAL,
        DAL.SysDAL.SysUserDAL sysUserDAL,
        WechatAccessTokenService wechatAccessTokenService,
        SystemConfigService systemConfigService,
        ILogger<WechatOAuthService> logger) : BasePermissionService(userDAL, null!)
    {
        private readonly new UserDAL _userDAL = userDAL;
        private readonly DAL.SysDAL.SysUserDAL _sysUserDAL = sysUserDAL;
        private readonly WechatAccessTokenService _wechatAccessTokenService = wechatAccessTokenService;
        private readonly SystemConfigService _systemConfigService = systemConfigService;
        private readonly ILogger<WechatOAuthService> _logger = logger;

        /// <summary>
        /// 生成微信OAuth授权URL
        /// </summary>
        /// <param name="requestDto">授权请求DTO</param>
        /// <returns>授权URL</returns>
        public string GenerateOAuthUrl(WechatOAuthRequestDto requestDto)
        {
            _logger.LogInformation("开始生成微信OAuth授权URL，RedirectUri: {RedirectUri}, Scope: {Scope}, State: {State}, InviterId: {InviterId}",
                requestDto.RedirectUri, requestDto.Scope, requestDto.State, requestDto.InviterId);

            var appId = WxSetting.AppId;
            if (string.IsNullOrEmpty(appId))
            {
                _logger.LogError("微信AppID未配置");
                throw new BusinessException("微信AppID未配置");
            }

            // 生成包含邀请人信息的State参数
            string finalState = requestDto.State ?? string.Empty;
            var stateInfo = new WechatOAuthStateDto
            {
                Timestamp = DateTimeOffset.UtcNow.ToUnixTimeSeconds(),
                Random = Guid.NewGuid().ToString("N")[..8],
                ReturnUrl = requestDto.State, // 原始State作为返回URL
                InviterId = requestDto.InviterId
            };

            var stateJson = JsonConvert.SerializeObject(stateInfo);
            finalState = Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes(stateJson));

            _logger.LogInformation("生成包含邀请人信息的State参数 - InviterId: {InviterId}, StateLength: {StateLength}位",
                requestDto.InviterId, finalState.Length);

            var authUrl = WechatOAuthHelper.GenerateOAuthUrl(appId, requestDto.RedirectUri, requestDto.Scope, finalState);
            _logger.LogInformation("成功生成微信OAuth授权URL: {AuthUrl}", authUrl);

            return authUrl;
        }

        /// <summary>
        /// 处理微信OAuth回调，完成用户登录
        /// </summary>
        /// <param name="callbackDto">回调DTO</param>
        /// <returns>登录响应</returns>
        public async Task<WechatOAuthLoginResponseDto> HandleOAuthCallbackAsync(WechatOAuthCallbackDto callbackDto)
        {
            _logger.LogInformation("=== 开始处理微信OAuth回调 ===");
            _logger.LogInformation("输入参数 - Code: {Code}, State: {State}, InviterId: {InviterId}",
                callbackDto.Code, callbackDto.State, callbackDto.InviterId);

            // 验证邀请人信息是否存在
            if (string.IsNullOrEmpty(callbackDto.InviterId))
            {
                _logger.LogError("邀请人ID为空，无法处理OAuth回调");
                throw new BusinessException("邀请人不能为空，只有员工可以邀请用户");
            }

            var appId = WxSetting.AppId;
            var appSecret = WxSetting.AppSecret;
            _logger.LogInformation("微信配置 - AppId: {AppId}, AppSecret: {AppSecretStatus}",
                appId, string.IsNullOrEmpty(appSecret) ? "未设置" : $"已设置({appSecret.Length}位)");

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret))
            {
                _logger.LogError("微信配置未完整设置，AppId: {AppId}, AppSecret: {AppSecretLength}",
                    appId, string.IsNullOrEmpty(appSecret) ? "未设置" : $"{appSecret.Length}位");
                throw new BusinessException("微信配置未完整设置");
            }

            // 1. 通过code换取access_token
            _logger.LogInformation("=== 步骤1：通过code换取access_token ===");
            _logger.LogInformation("请求参数 - AppId: {AppId}, Code: {Code}", appId, callbackDto.Code);
            var tokenResponse = await WechatOAuthHelper.GetOAuthAccessTokenAsync(appId, appSecret, callbackDto.Code);
            if (tokenResponse == null)
            {
                _logger.LogError("获取微信access_token失败，Code: {Code}", callbackDto.Code);
                throw new BusinessException("获取微信access_token失败");
            }
            _logger.LogInformation("步骤1完成 - AccessToken长度: {AccessTokenLength}位, OpenId: {OpenId}, UnionId: {UnionId}, ExpiresIn: {ExpiresIn}",
                tokenResponse.AccessToken?.Length ?? 0, tokenResponse.OpenId, tokenResponse.UnionId, tokenResponse.ExpiresIn);

            // 2. 获取用户信息
            _logger.LogInformation("=== 步骤2：获取微信用户信息 ===");
            _logger.LogInformation("请求参数 - AccessToken: {AccessTokenLength}位, OpenId: {OpenId}",
                tokenResponse.AccessToken?.Length ?? 0, tokenResponse.OpenId);
            var wechatUserInfo = await WechatOAuthHelper.GetUserInfoAsync(tokenResponse.AccessToken!, tokenResponse.OpenId);
            if (wechatUserInfo == null)
            {
                _logger.LogError("获取微信用户信息失败，OpenId: {OpenId}", tokenResponse.OpenId);
                throw new BusinessException("获取微信用户信息失败");
            }
            _logger.LogInformation("步骤2完成 - 昵称: {Nickname}, UnionId: {UnionId}, 性别: {Sex}, 城市: {City}, 头像: {Avatar}",
                wechatUserInfo.Nickname, wechatUserInfo.UnionId, wechatUserInfo.Sex, wechatUserInfo.City, wechatUserInfo.HeadImgUrl);

            // 3. 查找或创建用户
            _logger.LogInformation("=== 步骤3：查找或创建用户 ===");
            _logger.LogInformation("查找参数 - OpenId: {OpenId}, UnionId: {UnionId}",
                wechatUserInfo.OpenId, wechatUserInfo.UnionId);

            // 检查数据库连接
            try
            {
                var testCount = await _userDAL.GetCountAsync();
                _logger.LogInformation("数据库连接正常，当前用户总数: {UserCount}", testCount);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "数据库连接测试失败: {Message}", ex.Message);
                throw new BusinessException("数据库连接失败");
            }

            var user = await FindOrCreateUserAsync(wechatUserInfo, tokenResponse, callbackDto.InviterId);
            _logger.LogInformation("步骤3完成 - 用户ID: {UserId}, 昵称: {Nickname}, 是否新用户: {IsNewUser}, 审核状态: {AuditStatus}",
                user.Id, user.Nickname, user.CreateTime > DateTime.Now.AddMinutes(-1), user.AuditStatus);

            // 4. 生成JWT Token
            _logger.LogInformation("=== 步骤4：生成JWT Token ===");
            _logger.LogInformation("生成Token参数 - 用户ID: {UserId}", user.Id);
            var jwtToken = GenerateJwtToken(user);
            var userToken = UserJWTHelper.GenerateUserToken(user);
            _logger.LogInformation("步骤4完成 - 管理系统JWT Token长度: {TokenLength}位, 用户端Token长度: {UserTokenLength}位",
                jwtToken?.Length ?? 0, userToken?.Length ?? 0);

            // 5. 记录登录日志
            _logger.LogInformation("=== 步骤5：记录登录日志 ===");
            await LogLoginAsync(user, wechatUserInfo);
            _logger.LogInformation("步骤5完成 - 登录日志已记录");

            // 6. 构造响应数据
            _logger.LogInformation("=== 步骤6：构造响应数据 ===");
            var isNewUser = user.CreateTime > DateTime.Now.AddMinutes(-1); // 1分钟内创建的视为新用户
            var response = new WechatOAuthLoginResponseDto
            {
                AccessToken = jwtToken!,
                UserToken = userToken ?? string.Empty,
                UserTokenExpiryDays = 7,
                UserInfo = new UserResponseDto
                {
                    Id = user.Id,
                    OpenId = user.OpenId,
                    UnionId = user.UnionId,
                    Nickname = user.Nickname,
                    Avatar = user.Avatar,
                    EmployeeId = user.EmployeeId,
                    LastLogin = user.LastLogin,
                    CreateTime = user.CreateTime,
                    AuditStatus = user.AuditStatus,
                    InviterId = user.InviterId,
                    InviteTime = user.InviteTime
                },
                IsNewUser = isNewUser,
                AuditStatus = user.AuditStatus,
                InviterId = user.InviterId
            };

            _logger.LogInformation("步骤6完成 - 响应数据构造完成");
            _logger.LogInformation("=== 微信OAuth登录流程完成 ===");
            _logger.LogInformation("最终结果 - 用户ID: {UserId}, 昵称: {Nickname}, 是否新用户: {IsNewUser}, 审核状态: {AuditStatus}, 邀请人: {InviterId}",
                user.Id, user.Nickname, response.IsNewUser, user.AuditStatus, user.InviterId);
            _logger.LogInformation("Token信息 - 管理系统Token长度: {AccessTokenLength}位, 用户端Token长度: {UserTokenLength}位, 用户端Token有效期: {ExpiryDays}天",
                response.AccessToken?.Length ?? 0, response.UserToken?.Length ?? 0, response.UserTokenExpiryDays);

            return response;
        }

        /// <summary>
        /// 刷新微信基础access_token
        /// </summary>
        /// <returns>是否成功</returns>
        public async Task<bool> RefreshBasicAccessTokenAsync()
        {
            _logger.LogInformation("开始刷新微信基础access_token");

            var appId = WxSetting.AppId;
            var appSecret = WxSetting.AppSecret;

            if (string.IsNullOrEmpty(appId) || string.IsNullOrEmpty(appSecret))
            {
                _logger.LogError("微信配置未完整设置，无法刷新基础access_token");
                throw new BusinessException("微信配置未完整设置");
            }

            // 获取新的基础access_token
            _logger.LogInformation("开始获取新的基础access_token，AppId: {AppId}", appId);
            var tokenResponse = await WechatOAuthHelper.GetBasicAccessTokenAsync(appId, appSecret);
            if (tokenResponse == null)
            {
                _logger.LogError("获取微信基础access_token失败");
                return false;
            }
            _logger.LogInformation("成功获取微信基础access_token，ExpiresIn: {ExpiresIn}秒", tokenResponse.ExpiresIn);

            // 保存到数据库
            var createDto = new WechatAccessTokenCreateDto
            {
                AppId = appId,
                AccessToken = tokenResponse.AccessToken,
                ExpiresAt = DateTime.Now.AddSeconds(tokenResponse.ExpiresIn),
                ExpiresIn = tokenResponse.ExpiresIn,
                TokenType = "basic_access_token",
                IsValid = 1
            };

            // 系统自动创建用户，无需当前用户信息

            await _wechatAccessTokenService.CreateAccessTokenAsync(createDto, null!);
            return true;
        }

        /// <summary>
        /// 查找或创建用户
        /// </summary>
        /// <param name="wechatUserInfo">微信用户信息</param>
        /// <param name="tokenResponse">微信token响应</param>
        /// <param name="inviterId">邀请人ID</param>
        /// <returns>用户实体</returns>
        private async Task<User> FindOrCreateUserAsync(WechatUserInfoDto wechatUserInfo, WxAccessTokenDto tokenResponse, string? inviterId)
        {
            User? user = null;

            // 优先通过UnionID查找用户
            if (!string.IsNullOrEmpty(wechatUserInfo.UnionId))
            {
                _logger.LogInformation("通过UnionID查找用户: {UnionId}", wechatUserInfo.UnionId);
                user = await _userDAL.GetByUnionIdAsync(wechatUserInfo.UnionId);
                if (user != null)
                {
                    _logger.LogInformation("通过UnionID找到现有用户，用户ID: {UserId}, 昵称: {Nickname}", user.Id, user.Nickname);
                }
                else
                {
                    _logger.LogInformation("通过UnionID未找到用户: {UnionId}", wechatUserInfo.UnionId);
                }
            }

            // 如果通过UnionID没找到，再通过OpenID查找
            if (user == null)
            {
                _logger.LogInformation("通过OpenID查找用户: {OpenId}", wechatUserInfo.OpenId);
                user = await _userDAL.GetByOpenIdAsync(wechatUserInfo.OpenId);
                if (user != null)
                {
                    _logger.LogInformation("通过OpenID找到现有用户，用户ID: {UserId}, 昵称: {Nickname}", user.Id, user.Nickname);
                }
                else
                {
                    _logger.LogInformation("通过OpenID未找到用户: {OpenId}", wechatUserInfo.OpenId);
                }
            }

            if (user != null)
            {
                // 更新现有用户信息
                _logger.LogInformation("更新现有用户信息，用户ID: {UserId}, 原昵称: {OldNickname}, 新昵称: {NewNickname}",
                    user.Id, user.Nickname, wechatUserInfo.Nickname);

                user.OpenId = wechatUserInfo.OpenId;
                user.UnionId = wechatUserInfo.UnionId ?? tokenResponse.UnionId;
                user.Nickname = wechatUserInfo.Nickname;
                user.Avatar = wechatUserInfo.HeadImgUrl;
                user.LastLogin = DateTime.Now;
                user.UpdateTime = DateTime.Now;

                try
                {
                    _logger.LogInformation("准备更新用户信息到数据库，用户ID: {UserId}", user.Id);
                    var updateResult = await _userDAL.UpdateAsync(user);
                    _logger.LogInformation("数据库更新结果: {UpdateResult}", updateResult);

                    if (updateResult)
                    {
                        _logger.LogInformation("成功更新用户信息，用户ID: {UserId}", user.Id);
                    }
                    else
                    {
                        _logger.LogError("数据库更新失败，UpdateAsync返回false，用户ID: {UserId}", user.Id);
                        throw new BusinessException("用户信息更新失败：数据库更新失败");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "更新用户信息时发生异常，用户ID: {UserId}, 异常信息: {Message}", user.Id, ex.Message);
                    throw new BusinessException($"用户信息更新失败：{ex.Message}");
                }
            }
            else
            {
                // 创建新用户
                _logger.LogInformation("创建新用户，OpenId: {OpenId}, UnionId: {UnionId}, 昵称: {Nickname}, 邀请人: {InviterId}",
                    wechatUserInfo.OpenId, wechatUserInfo.UnionId ?? tokenResponse.UnionId, wechatUserInfo.Nickname, inviterId);

                // 邀请人是必须参数，验证邀请人是否存在且为员工
                if (string.IsNullOrEmpty(inviterId))
                {
                    _logger.LogError("邀请人ID为空，无法创建用户");
                    throw new BusinessException("邀请人不能为空，只有员工可以邀请用户");
                }

                _logger.LogInformation("验证邀请人是否存在: {InviterId}", inviterId);
                var inviter = await _sysUserDAL.GetFirstAsync(new DAL.SysDAL.SysUserDAL.UserDALQuery { UserId = inviterId });
                if (inviter == null)
                {
                    _logger.LogError("邀请人不存在: {InviterId}", inviterId);
                    throw new BusinessException("邀请人不存在，只有员工可以邀请用户");
                }

                // 验证用户类型是否为员工(UserType=3)或超级管理员(UserType=1)
                if (inviter.UserType != 3 && inviter.UserType != 1)
                {
                    var invalidUserTypeText = inviter.UserType switch
                    {
                        2 => "管理员",
                        _ => "未知类型"
                    };
                    _logger.LogError("邀请人类型不符合要求 - ID: {InviterId}, 姓名: {RealName}, 类型: {UserTypeText}({UserType})",
                        inviter.UserId, inviter.RealName, invalidUserTypeText, inviter.UserType);
                    throw new BusinessException("邀请人身份异常，只有员工可以邀请用户");
                }

                // 验证邀请人状态是否正常
                if (inviter.Status != 1)
                {
                    var statusText = inviter.Status switch
                    {
                        0 => "已禁用",
                        2 => "待审核",
                        _ => "未知状态"
                    };
                    _logger.LogError("邀请人状态异常 - ID: {InviterId}, 姓名: {RealName}, 状态: {StatusText}({Status})",
                        inviter.UserId, inviter.RealName, statusText, inviter.Status);
                    throw new BusinessException("邀请人状态异常，无法进行邀请");
                }

                var userTypeText = inviter.UserType == 1 ? "超级管理员" : "员工";
                _logger.LogInformation("邀请人验证成功 - ID: {InviterId}, 姓名: {RealName}, 类型: {UserTypeText}({UserType})",
                    inviter.UserId, inviter.RealName, userTypeText, inviter.UserType);

                // 检查是否开启自动审核
                var isAutoAuditEnabled = await _systemConfigService.IsAutoAuditEnabledAsync();
                var auditStatus = isAutoAuditEnabled ? 1 : 0; // 1=已通过, 0=待审核

                _logger.LogInformation("审核配置检查 - 自动审核: {IsAutoAuditEnabled}, 用户审核状态: {AuditStatus}",
                    isAutoAuditEnabled, auditStatus);

                user = new User
                {
                    OpenId = wechatUserInfo.OpenId,
                    UnionId = wechatUserInfo.UnionId ?? tokenResponse.UnionId,
                    Nickname = wechatUserInfo.Nickname,
                    Avatar = wechatUserInfo.HeadImgUrl,
                    LastLogin = DateTime.Now,
                    CreateTime = DateTime.Now,
                    CreatedBy = "WECHAT_OAUTH",
                    CreatorName = "微信授权登录",
                    AuditStatus = auditStatus,
                    InviterId = inviterId, // 使用验证通过的邀请人ID
                    EmployeeId = inviterId, // 同时绑定到EmployeeId字段
                    InviteTime = DateTime.Now
                };

                try
                {
                    _logger.LogInformation("准备保存新用户到数据库，用户ID: {UserId}, OpenId: {OpenId}, UnionId: {UnionId}, 昵称: {Nickname}, 审核状态: {AuditStatus}, 邀请人: {InviterId}, 员工ID: {EmployeeId}",
                        user.Id, user.OpenId, user.UnionId, user.Nickname, user.AuditStatus, user.InviterId, user.EmployeeId);

                    var addResult = await _userDAL.AddAsync(user);
                    _logger.LogInformation("数据库保存结果: {AddResult}", addResult);

                    if (addResult)
                    {
                        _logger.LogInformation("成功创建新用户，用户ID: {UserId}, 昵称: {Nickname}, 审核状态: {AuditStatus}, 邀请人: {InviterId}, 员工ID: {EmployeeId}",
                            user.Id, user.Nickname, user.AuditStatus, user.InviterId, user.EmployeeId);
                    }
                    else
                    {
                        _logger.LogError("数据库保存失败，AddAsync返回false，用户ID: {UserId}", user.Id);
                        throw new BusinessException("用户创建失败：数据库保存失败");
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "创建用户时发生异常，用户ID: {UserId}, OpenId: {OpenId}, UnionId: {UnionId}, 异常信息: {Message}",
                        user.Id, user.OpenId, user.UnionId, ex.Message);
                    throw new BusinessException($"用户创建失败：{ex.Message}");
                }
            }

            return user;
        }

        /// <summary>
        /// 解析State参数
        /// </summary>
        /// <param name="state">State参数字符串</param>
        /// <returns>解析后的State信息</returns>
        private WechatOAuthStateDto? ParseStateParameter(string? state)
        {
            if (string.IsNullOrEmpty(state))
            {
                _logger.LogInformation("State参数为空，跳过解析");
                return null;
            }

            try
            {
                _logger.LogInformation("开始解析State参数: {State}", state);

                // 尝试Base64解码（如果State是Base64编码的）
                string decodedState = state;
                try
                {
                    var base64Bytes = Convert.FromBase64String(state);
                    decodedState = System.Text.Encoding.UTF8.GetString(base64Bytes);
                    _logger.LogInformation("State参数Base64解码成功: {DecodedState}", decodedState);
                }
                catch
                {
                    _logger.LogInformation("State参数不是Base64格式，直接解析JSON");
                }

                var stateInfo = JsonConvert.DeserializeObject<WechatOAuthStateDto>(decodedState);
                _logger.LogInformation("State参数解析成功 - InviterId: {InviterId}, ReturnUrl: {ReturnUrl}, Timestamp: {Timestamp}",
                    stateInfo?.InviterId, stateInfo?.ReturnUrl, stateInfo?.Timestamp);

                return stateInfo;
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "解析State参数失败: {State}", state);
                return null;
            }
        }

        /// <summary>
        /// 生成JWT Token
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT Token</returns>
        private static string GenerateJwtToken(User user)
        {
            var userInfo = new UserInfo
            {
                UserId = user.Id,
                UserName = user.Nickname ?? $"用户{user.Id}",
                IsAdmin = false,
                UserType = 4, // 普通用户类型
                Roles = []
            };

            return JWTHelper.CreateJwt(userInfo);
        }

        /// <summary>
        /// 记录登录日志
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <param name="wechatUserInfo">微信用户信息</param>
        private async Task LogLoginAsync(User user, WechatUserInfoDto wechatUserInfo)
        {
            try
            {
                _logger.LogInformation("用户登录成功 - 用户ID: {UserId}, 昵称: {Nickname}, OpenId: {OpenId}, UnionId: {UnionId}, 员工ID: {EmployeeId}, 登录时间: {LoginTime}",
                    user.Id, user.Nickname, user.OpenId, user.UnionId, user.EmployeeId, user.LastLogin);

                // 记录详细的微信用户信息
                _logger.LogDebug("微信用户详细信息 - 性别: {Sex}, 省份: {Province}, 城市: {City}, 国家: {Country}, 头像: {Avatar}",
                    wechatUserInfo.Sex, wechatUserInfo.Province, wechatUserInfo.City, wechatUserInfo.Country, wechatUserInfo.HeadImgUrl);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "记录登录日志时发生错误，用户ID: {UserId}", user.Id);
            }

            await Task.CompletedTask;
        }

        /// <summary>
        /// 验证邀请人身份
        /// </summary>
        /// <param name="inviterId">邀请人ID</param>
        /// <returns>验证结果</returns>
        public async Task<(bool IsValid, string ErrorMessage)> ValidateInviterAsync(string inviterId)
        {
            if (string.IsNullOrEmpty(inviterId))
            {
                return (false, "邀请人ID不能为空");
            }

            _logger.LogInformation("验证邀请人身份: {InviterId}", inviterId);

            var inviter = await _sysUserDAL.GetFirstAsync(new DAL.SysDAL.SysUserDAL.UserDALQuery { UserId = inviterId });
            if (inviter == null)
            {
                _logger.LogWarning("邀请人不存在: {InviterId}", inviterId);
                return (false, "邀请人不存在");
            }

            // 验证用户类型是否为员工(UserType=3)或超级管理员(UserType=1)
            if (inviter.UserType != 3 && inviter.UserType != 1)
            {
                var invalidUserTypeText = inviter.UserType switch
                {
                    2 => "管理员",
                    _ => "未知类型"
                };
                _logger.LogWarning("邀请人类型不符合要求 - ID: {InviterId}, 姓名: {RealName}, 类型: {UserTypeText}({UserType})",
                    inviter.UserId, inviter.RealName, invalidUserTypeText, inviter.UserType);
                return (false, "邀请人身份异常，只有员工才能邀请用户");
            }

            // 验证用户状态是否正常
            if (inviter.Status != 1)
            {
                var statusText = inviter.Status switch
                {
                    0 => "已禁用",
                    2 => "待审核",
                    _ => "未知状态"
                };
                _logger.LogWarning("邀请人状态异常 - ID: {InviterId}, 姓名: {RealName}, 状态: {StatusText}({Status})",
                    inviter.UserId, inviter.RealName, statusText, inviter.Status);
                return (false, "邀请人状态异常，无法进行邀请");
            }

            var userTypeText = inviter.UserType == 1 ? "超级管理员" : "员工";
            _logger.LogInformation("邀请人验证成功 - ID: {InviterId}, 姓名: {RealName}, 类型: {UserTypeText}({UserType})",
                inviter.UserId, inviter.RealName, userTypeText, inviter.UserType);

            return (true, string.Empty);
        }
    }
}
