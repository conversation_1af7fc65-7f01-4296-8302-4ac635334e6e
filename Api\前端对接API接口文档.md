# 前端对接API接口文档

## 基础信息

**服务器地址**: `https://localhost:7048` (开发环境)  
**API前缀**: `/api`  
**数据格式**: JSON  
**字符编码**: UTF-8  

## 通用响应格式

```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "code": 200
}
```

## 1. 微信OAuth登录相关接口

### 1.1 获取微信授权URL
**接口地址**: `GET /api/Wechat/auth-url`  
**接口说明**: 生成微信OAuth2.0授权链接  
**权限要求**: 无需认证  

**请求参数**:
```
Query参数:
- redirectUri: string (必填) - 授权回调地址
- state: string (可选) - 自定义状态参数，用于传递额外信息
```

**请求示例**:
```
GET /api/Wechat/auth-url?redirectUri=https://yourdomain.com/callback&state=inviter_emp_001
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "authUrl": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx02bacea38f7f3ab5&redirect_uri=https%3a%2f%2fyourdomain.com%2fcallback&response_type=code&scope=snsapi_userinfo&state=inviter_emp_001#wechat_redirect"
  },
  "message": "授权URL生成成功"
}
```

### 1.2 微信OAuth回调处理
**接口地址**: `GET /api/Wechat/callback`  
**接口说明**: 处理微信OAuth回调，完成用户登录/注册  
**权限要求**: 无需认证  

**请求参数**:
```
Query参数:
- code: string (必填) - 微信授权码
- state: string (可选) - 状态参数，格式: inviter_员工ID
```

**请求示例**:
```
GET /api/Wechat/callback?code=021234567890&state=inviter_emp_001
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "userInfo": {
      "id": "user_12345678901234567890123456789012",
      "openId": "oABC123456789",
      "unionId": "uABC123456789",
      "nickname": "张三",
      "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
      "auditStatus": 0,
      "inviterId": "emp_001",
      "inviteTime": "2025-08-03T21:30:00"
    },
    "userToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "tokenExpiry": "2025-08-04T21:30:00"
  },
  "message": "登录成功"
}
```

### 1.3 检查Token状态
**接口地址**: `GET /api/Wechat/token-status`  
**接口说明**: 检查用户Token是否有效  
**权限要求**: 需要用户Token  

**请求头**:
```
Authorization: Bearer {userToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "isValid": true,
    "userId": "user_12345678901234567890123456789012",
    "expiryTime": "2025-08-04T21:30:00",
    "auditStatus": 1
  },
  "message": "Token有效"
}
```

## 2. 用户审核相关接口

### 2.1 员工审核用户
**接口地址**: `POST /api/UserAudit/audit-user/{userId}`  
**接口说明**: 员工审核用户申请  
**权限要求**: 需要管理员Token  

**请求头**:
```
Authorization: Bearer {adminToken}
```

**请求参数**:
```json
{
  "status": 1,
  "remark": "审核通过"
}
```

**参数说明**:
- status: int (必填) - 审核状态 (1=通过, 2=拒绝)
- remark: string (可选) - 审核备注

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "用户审核成功"
}
```

### 2.2 获取员工的待审核用户列表
**接口地址**: `GET /api/UserAudit/pending-users`  
**接口说明**: 获取当前员工邀请的待审核用户列表  
**权限要求**: 需要管理员Token  

**请求头**:
```
Authorization: Bearer {adminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user_12345678901234567890123456789012",
      "nickname": "张三",
      "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
      "auditStatus": 0,
      "inviteTime": "2025-08-03T21:30:00",
      "inviterName": "员工001"
    }
  ],
  "message": "获取成功"
}
```

### 2.3 获取所有待审核用户列表 (管理员)
**接口地址**: `GET /api/UserAudit/all-pending-users`  
**接口说明**: 获取所有待审核用户列表 (仅超级管理员)  
**权限要求**: 需要超级管理员Token  

**请求头**:
```
Authorization: Bearer {superAdminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "user_12345678901234567890123456789012",
      "nickname": "张三",
      "avatar": "https://thirdwx.qlogo.cn/mmopen/...",
      "auditStatus": 0,
      "inviteTime": "2025-08-03T21:30:00",
      "inviterName": "员工001"
    }
  ],
  "message": "获取成功"
}
```

## 3. 视频观看进度相关接口

### 3.1 更新视频观看进度
**接口地址**: `POST /api/UserBatchRecord/{userId}/watch-progress`
**接口说明**: 更新用户视频观看进度
**权限要求**: 无需认证 (匿名访问)

**请求参数**:
```json
{
  "batchId": 1,
  "viewDuration": 120,
  "watchProgress": 0.85,
  "isCompleted": false
}
```

**参数说明**:
- batchId: int (必填) - 批次ID
- viewDuration: int (必填) - 观看时长(秒)
- watchProgress: decimal (必填) - 观看进度 (0-1之间的小数，如0.85表示85%)
- isCompleted: bool (必填) - 是否完成观看

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "观看进度更新成功"
}
```

### 3.2 获取用户观看状态
**接口地址**: `GET /api/UserBatchRecord/{userId}/{batchId}/watch-status`
**接口说明**: 获取用户在指定批次的观看状态
**权限要求**: 无需认证 (匿名访问)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "hasRecord": true,
    "hasWatched": true,
    "isCompleted": false,
    "watchProgress": 0.85,
    "watchProgressPercent": 85.0,
    "viewDuration": 120,
    "startTime": "2025-08-03T21:30:00",
    "lastWatchTime": "2025-08-03T21:32:00"
  },
  "message": "观看状态获取成功"
}
```

### 3.3 开始观看 (记录开始时间)
**接口地址**: `POST /api/UserBatchRecord/{userId}/{batchId}/start-watching`
**接口说明**: 记录用户开始观看的时间
**权限要求**: 无需认证 (匿名访问)

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "开始观看记录成功"
}
```

### 3.4 批量更新观看进度
**接口地址**: `POST /api/UserBatchRecord/batch-update-progress`
**接口说明**: 批量更新多个用户的观看进度 (管理员功能)
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**请求参数**:
```json
[
  {
    "userId": "user_12345678901234567890123456789012",
    "batchId": 1,
    "viewDuration": 120,
    "watchProgress": 0.85,
    "isCompleted": false
  },
  {
    "userId": "user_98765432109876543210987654321098",
    "batchId": 1,
    "viewDuration": 300,
    "watchProgress": 1.0,
    "isCompleted": true
  }
]
```

**响应示例**:
```json
{
  "success": true,
  "data": 2,
  "message": "批量更新完成，成功更新 2 条记录"
}
```

## 4. 答题相关接口

### 4.1 提交答题结果
**接口地址**: `POST /api/UserBatchRecord/{userId}/submit-answer`  
**接口说明**: 提交用户答题结果  
**权限要求**: 需要用户Token  

**请求头**:
```
Authorization: Bearer {userToken}
```

**请求参数**:
```json
{
  "batchId": 1,
  "totalQuestions": 10,
  "correctAnswers": 8,
  "answerDetails": "{\"q1\":\"A\",\"q2\":\"B\",\"q3\":\"C\"}"
}
```

**参数说明**:
- batchId: int (必填) - 批次ID
- totalQuestions: int (必填) - 总题目数
- correctAnswers: int (必填) - 正确答案数
- answerDetails: string (必填) - 答题详情JSON字符串

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "答题结果提交成功"
}
```

### 4.2 获取用户答题状态
**接口地址**: `GET /api/UserBatchRecord/{userId}/{batchId}/answer-status`
**接口说明**: 获取用户在指定批次的答题状态
**权限要求**: 无需认证 (匿名访问)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "hasRecord": true,
    "hasAnswered": true,
    "totalQuestions": 10,
    "correctAnswers": 8,
    "correctRate": 0.8,
    "answerTime": "2025-08-03T21:35:00",
    "canAnswer": false
  },
  "message": "答题状态获取成功"
}
```

**字段说明**:
- hasRecord: bool - 是否有记录
- hasAnswered: bool - 是否已答题
- totalQuestions: int - 总题目数
- correctAnswers: int - 正确答案数
- correctRate: decimal - 正确率 (0-1之间)
- answerTime: DateTime? - 答题时间
- canAnswer: bool - 是否可以答题 (只有完播后才能答题)

## 5. 红包相关接口

### 5.1 发放红包
**接口地址**: `POST /api/UserBatchRecord/{userId}/grant-reward`  
**接口说明**: 为用户发放红包  
**权限要求**: 需要管理员Token  

**请求头**:
```
Authorization: Bearer {adminToken}
```

**请求参数**:
```json
{
  "batchId": 1,
  "rewardAmount": 10.50,
  "transactionId": "tx_123456789",
  "outTradeNo": "out_123456789"
}
```

**参数说明**:
- batchId: int (必填) - 批次ID
- rewardAmount: decimal (必填) - 红包金额
- transactionId: string (可选) - 交易ID
- outTradeNo: string (可选) - 商户订单号

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "红包发放成功"
}
```

### 5.2 获取用户红包状态
**接口地址**: `GET /api/UserBatchRecord/{userId}/{batchId}/reward-status`
**接口说明**: 获取用户在指定批次的红包状态
**权限要求**: 无需认证 (匿名访问)

**响应示例**:
```json
{
  "success": true,
  "data": {
    "hasRecord": true,
    "hasReward": true,
    "rewardAmount": 10.50,
    "rewardStatus": 1,
    "rewardStatusText": "发放成功",
    "rewardTime": "2025-08-03T21:40:00",
    "transactionId": "tx_123456789",
    "outTradeNo": "out_123456789"
  },
  "message": "红包状态获取成功"
}
```

**字段说明**:
- hasRecord: bool - 是否有记录
- hasReward: bool - 是否已获得红包
- rewardAmount: decimal - 红包金额
- rewardStatus: int - 红包状态 (0=未发放, 1=发放成功, 2=发放失败)
- rewardStatusText: string - 红包状态文本
- rewardTime: DateTime? - 红包发放时间
- transactionId: string? - 微信支付交易号
- outTradeNo: string? - 商户订单号

### 5.3 更新红包状态
**接口地址**: `POST /api/UserBatchRecord/{userId}/update-reward-status`
**接口说明**: 更新用户红包发放状态 (管理员功能)
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**请求参数**:
```json
{
  "batchId": 1,
  "rewardStatus": 1,
  "failReason": null,
  "transactionId": "tx_123456789",
  "outTradeNo": "out_123456789"
}
```

**参数说明**:
- batchId: int (必填) - 批次ID
- rewardStatus: int (必填) - 红包状态 (0=未发放, 1=发放成功, 2=发放失败)
- failReason: string (可选) - 失败原因 (状态为2时必填)
- transactionId: string (可选) - 微信支付交易号
- outTradeNo: string (可选) - 微信支付订单号

**响应示例**:
```json
{
  "success": true,
  "data": true,
  "message": "红包状态更新成功"
}
```

## 6. 批次管理接口

### 6.1 获取批次列表
**接口地址**: `GET /api/Batch`
**接口说明**: 获取批次列表
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "第一期视频学习",
      "description": "企业培训第一期",
      "status": 1,
      "createTime": "2025-08-03T10:00:00",
      "videoCount": 5,
      "userCount": 100
    }
  ],
  "message": "获取批次列表成功"
}
```

### 6.2 获取批次详情
**接口地址**: `GET /api/Batch/{id}`
**接口说明**: 获取指定批次的详细信息
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "name": "第一期视频学习",
    "description": "企业培训第一期",
    "status": 1,
    "createTime": "2025-08-03T10:00:00",
    "videos": [
      {
        "id": "video_001",
        "title": "企业文化介绍",
        "duration": 300,
        "url": "https://example.com/video1.mp4"
      }
    ]
  },
  "message": "获取批次详情成功"
}
```

## 7. 视频管理接口

### 7.1 获取视频列表
**接口地址**: `GET /api/Video`
**接口说明**: 获取视频列表
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": [
    {
      "id": "video_001",
      "title": "企业文化介绍",
      "description": "公司企业文化详细介绍",
      "duration": 300,
      "url": "https://example.com/video1.mp4",
      "status": 1,
      "createTime": "2025-08-03T10:00:00"
    }
  ],
  "message": "获取视频列表成功"
}
```

### 7.2 获取视频详情
**接口地址**: `GET /api/Video/{id}`
**接口说明**: 获取指定视频的详细信息
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": "video_001",
    "title": "企业文化介绍",
    "description": "公司企业文化详细介绍",
    "duration": 300,
    "url": "https://example.com/video1.mp4",
    "status": 1,
    "createTime": "2025-08-03T10:00:00",
    "watchCount": 50,
    "completeCount": 45
  },
  "message": "获取视频详情成功"
}
```

## 8. 系统配置接口

### 8.1 获取系统配置
**接口地址**: `GET /api/SystemConfig/config/{key}`
**接口说明**: 获取指定的系统配置
**权限要求**: 需要管理员Token

**请求头**:
```
Authorization: Bearer {adminToken}
```

**请求示例**:
```
GET /api/SystemConfig/config/AUTO_AUDIT_ENABLED
```

**响应示例**:
```json
{
  "success": true,
  "data": {
    "id": 1,
    "configKey": "AUTO_AUDIT_ENABLED",
    "configValue": "false",
    "description": "是否启用自动审核"
  },
  "message": "获取配置成功"
}
```

## 9. Token说明

### 9.1 管理员Token
- **获取方式**: 通过管理后台登录接口 `/api/Auth/login`
- **用途**: 用于员工审核、红包发放等管理功能
- **有效期**: 24小时
- **格式**: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

### 9.2 用户Token
- **获取方式**: 通过微信OAuth回调自动生成
- **用途**: 用于用户侧功能(观看视频、答题等)
- **有效期**: 24小时
- **格式**: `Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`

## 10. 错误码说明

| 错误码 | 说明 |
|--------|------|
| 400 | 请求参数错误 |
| 401 | 未授权/Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 11. 状态码说明

### 审核状态 (AuditStatus)
- 0: 待审核
- 1: 审核通过
- 2: 审核拒绝

### 用户类型 (UserType)
- 1: 超级管理员
- 2: 普通管理员
- 3: 员工
- 4: 普通用户

### 红包状态 (RewardStatus)
- 0: 未发放
- 1: 发放成功
- 2: 发放失败

### 批次状态 (BatchStatus)
- 0: 下线
- 1: 上线

## 12. 完整业务流程示例

### 12.1 用户注册登录流程
```
1. 前端调用 /api/Wechat/auth-url 获取微信授权链接
2. 用户点击链接进行微信授权
3. 微信回调到 /api/Wechat/callback
4. 系统返回用户信息和userToken
5. 前端保存userToken用于后续API调用
```

### 12.2 员工审核流程
```
1. 员工使用管理员Token调用 /api/UserAudit/pending-users 获取待审核用户
2. 员工调用 /api/UserAudit/audit-user/{userId} 进行审核
3. 用户审核状态更新，可以正常使用系统功能
```

### 12.3 用户观看视频答题领红包流程
```
1. 用户调用 /api/UserBatchRecord/{userId}/{batchId}/start-watching 开始观看
2. 用户定期调用 /api/UserBatchRecord/{userId}/watch-progress 更新观看进度
3. 用户完成观看后调用 /api/UserBatchRecord/{userId}/submit-answer 提交答题
4. 管理员调用 /api/UserBatchRecord/{userId}/grant-reward 发放红包
5. 可通过状态查询接口实时获取各阶段状态
```

## 13. 重要注意事项

### 13.1 State参数使用说明
在微信OAuth流程中，state参数用于传递邀请人信息：
- **格式**: `inviter_{员工ID}`
- **示例**: `inviter_emp_001` 表示员工emp_001邀请的用户
- **作用**: 系统会自动将新用户绑定到对应的邀请员工

### 13.2 Token使用注意事项
- 所有需要认证的接口都必须在请求头中携带Token
- Token格式: `Authorization: Bearer {token}`
- Token过期后需要重新获取
- 用户Token和管理员Token不能混用

### 13.3 跨域配置
如果前端域名与API域名不同，需要配置CORS：
```javascript
// 前端请求示例
fetch('https://localhost:7048/api/Wechat/auth-url', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer ' + token
  }
})
```

### 13.4 错误处理建议
```javascript
// 统一错误处理示例
async function apiRequest(url, options = {}) {
  try {
    const response = await fetch(url, {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      },
      ...options
    });

    const result = await response.json();

    if (!result.success) {
      throw new Error(result.message || '请求失败');
    }

    return result.data;
  } catch (error) {
    console.error('API请求错误:', error);
    throw error;
  }
}
```

## 14. 测试用例

### 14.1 微信登录测试
```javascript
// 1. 获取授权URL
const authUrlResponse = await apiRequest('/api/Wechat/auth-url?redirectUri=https://test.com&state=inviter_emp_001');
console.log('授权URL:', authUrlResponse.authUrl);

// 2. 模拟回调处理 (实际由微信回调)
const callbackResponse = await apiRequest('/api/Wechat/callback?code=test_code&state=inviter_emp_001');
console.log('用户信息:', callbackResponse.userInfo);
console.log('用户Token:', callbackResponse.userToken);
```

### 14.2 用户审核测试
```javascript
// 获取待审核用户
const pendingUsers = await apiRequest('/api/UserAudit/pending-users', {
  headers: { 'Authorization': 'Bearer ' + adminToken }
});

// 审核用户
const auditResult = await apiRequest('/api/UserAudit/audit-user/' + userId, {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + adminToken },
  body: JSON.stringify({ status: 1, remark: '审核通过' })
});
```

### 14.3 视频观看测试
```javascript
// 记录观看进度
const progressResult = await apiRequest('/api/UserBatchRecord/' + userId + '/watch-progress', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + userToken },
  body: JSON.stringify({
    videoId: 'video_001',
    watchProgress: 100.0,
    watchDuration: 300
  })
});
```

### 14.4 答题测试
```javascript
// 提交答题结果
const answerResult = await apiRequest('/api/UserBatchRecord/' + userId + '/submit-answer', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + userToken },
  body: JSON.stringify({
    batchId: 1,
    totalQuestions: 10,
    correctAnswers: 8,
    answerDetails: JSON.stringify({q1: 'A', q2: 'B', q3: 'C'})
  })
});
```

### 14.5 红包发放测试
```javascript
// 发放红包
const rewardResult = await apiRequest('/api/UserBatchRecord/' + userId + '/grant-reward', {
  method: 'POST',
  headers: { 'Authorization': 'Bearer ' + adminToken },
  body: JSON.stringify({
    batchId: 1,
    rewardAmount: 10.50,
    transactionId: 'tx_' + Date.now(),
    outTradeNo: 'out_' + Date.now()
  })
});
```

## 15. 常见问题FAQ

### Q1: 微信授权后用户信息为空怎么办？
A: 检查微信应用配置，确保AppId和AppSecret正确，并且回调域名已在微信后台配置。

### Q2: Token过期如何处理？
A: 用户Token过期需要重新进行微信授权；管理员Token过期需要重新登录管理后台。

### Q3: 跨域问题如何解决？
A: 确保API服务器已配置CORS，允许前端域名访问。

### Q4: 如何调试接口问题？
A: 查看服务器日志文件，位置在 `logs/debug/` 和 `logs/error/` 目录下。

### Q5: 用户审核状态如何实时更新？
A: 建议前端定期轮询用户状态接口，或者使用WebSocket实现实时通知。

---

**文档版本**: v1.0
**更新时间**: 2025-08-03
**联系方式**: 如有问题请查看服务器日志或联系开发团队
