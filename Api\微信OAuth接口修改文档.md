# 微信OAuth接口文档

## 获取微信OAuth授权URL

**接口地址：** `POST /api/Wechat/oauth-url`

### 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| redirectUri | string | 是 | 授权后重定向的回调链接地址 |
| state | string | 否 | 状态参数，最多128字符 |
| scope | string | 否 | 授权作用域，默认为 snsapi_userinfo |
| inviterId | string | 是 | 邀请人ID（员工ID） |

### 请求示例

```javascript
const response = await fetch('/api/Wechat/oauth-url', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        redirectUri: 'https://example.com/callback',
        state: 'some-state',
        scope: 'snsapi_userinfo',
        inviterId: 'emp001'
    })
});
```

### 返回参数



#### 错误响应
```json
{
    "success": false,
    "code": 500,
    "message": "邀请人异常"
}
```

**注意：** 错误响应的HTTP状态码为200，通过 `success` 和 `code` 字段判断结果。

### 可能的错误消息

| 错误消息 | 说明 |
|----------|------|
| "邀请人ID不能为空" | 请求体中未提供 inviterId 参数 |
| "邀请人异常" | 邀请人不存在、不是员工身份或状态异常 |
| "参数验证失败" | 其他必填参数缺失或格式错误 |
