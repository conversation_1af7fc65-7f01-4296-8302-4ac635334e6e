using BLL.VideoService;
using Common.Log4Net;
using Entity.Dto;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 系统配置控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class SystemConfigController(SystemConfigService systemConfigService) : BaseController
    {
        private readonly SystemConfigService _systemConfigService = systemConfigService;

        /// <summary>
        /// 添加系统配置
        /// </summary>
        /// <param name="createDto">创建系统配置DTO</param>
        /// <returns>配置ID</returns>
        [HttpPost(Name = "SystemConfig_Add")]
        public async Task<Result<int>> AddSystemConfig([FromBody] SystemConfigCreateDto createDto)
        {
            var configId = await _systemConfigService.CreateSystemConfigAsync(createDto, GetCurrentUserInfo());
            return Success(configId, "系统配置添加成功");
        }

        /// <summary>
        /// 更新系统配置
        /// </summary>
        /// <param name="updateDto">更新系统配置DTO</param>
        /// <returns>是否成功</returns>
        [HttpPost("update", Name = "SystemConfig_Update")]
        public async Task<Result<bool>> UpdateSystemConfig([FromBody] SystemConfigUpdateDto updateDto)
        {
            var result = await _systemConfigService.UpdateSystemConfigAsync(updateDto, GetCurrentUserInfo());
            return Success(result, "系统配置更新成功");
        }

        /// <summary>
        /// 删除系统配置
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <returns>是否成功</returns>
        [HttpDelete("{configId}", Name = "SystemConfig_Delete")]
        public async Task<Result<bool>> DeleteSystemConfig(int configId)
        {
            var result = await _systemConfigService.DeleteSystemConfigAsync(configId, GetCurrentUserInfo());
            return Success(result, "系统配置删除成功");
        }

        /// <summary>
        /// 获取系统配置详情
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <returns>配置详情</returns>
        [HttpGet("{configId}")]
        public async Task<Result<SystemConfigResponseDto?>> GetSystemConfig(int configId)
        {
            var config = await _systemConfigService.GetSystemConfigAsync(configId);
            return Success(config);
        }

        /// <summary>
        /// 根据配置键获取配置
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置详情</returns>
        [HttpGet("key/{configKey}")]
        public async Task<Result<SystemConfigResponseDto?>> GetSystemConfigByKey(string configKey)
        {
            var config = await _systemConfigService.GetSystemConfigByKeyAsync(configKey);
            return Success(config);
        }

        /// <summary>
        /// 获取配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <returns>配置值</returns>
        [HttpGet("value/{configKey}")]
        [AllowAnonymous]
        public async Task<Result<string?>> GetConfigValue(string configKey)
        {
            var value = await _systemConfigService.GetConfigValueAsync(configKey);
            return Success(value, "获取配置值成功");
        }

        /// <summary>
        /// 设置配置值
        /// </summary>
        /// <param name="configKey">配置键</param>
        /// <param name="configValue">配置值</param>
        /// <returns>是否成功</returns>
        [HttpPost("value/{configKey}")]
        public async Task<Result<bool>> SetConfigValue(string configKey, [FromBody] string configValue)
        {
            var result = await _systemConfigService.SetConfigValueAsync(configKey, configValue, GetCurrentUserInfo());
            return Success(result, "配置值设置成功");
        }

        /// <summary>
        /// 分页查询系统配置列表
        /// </summary>
        /// <param name="queryDto">查询条件</param>
        /// <returns>配置列表</returns>
        [HttpGet]
        public async Task<Result<PagedResult<SystemConfigResponseDto>>> GetSystemConfigPagedList([FromQuery] SystemConfigQueryDto queryDto)
        {
            var result = await _systemConfigService.GetSystemConfigPagedListAsync(queryDto);
            return Success(result);
        }

        /// <summary>
        /// 获取所有系统配置
        /// </summary>
        /// <returns>配置列表</returns>
        [HttpGet("all")]
        public async Task<Result<List<SystemConfigResponseDto>>> GetAllSystemConfigs()
        {
            var configs = await _systemConfigService.GetAllSystemConfigsAsync();
            return Success(configs);
        }

        /// <summary>
        /// 根据分组获取配置
        /// </summary>
        /// <param name="groupName">配置分组</param>
        /// <returns>配置列表</returns>
        [HttpGet("group/{groupName}")]
        public async Task<Result<List<SystemConfigResponseDto>>> GetConfigsByGroup(string groupName)
        {
            var configs = await _systemConfigService.GetConfigsByGroupAsync(groupName);
            return Success(configs);
        }

        /// <summary>
        /// 根据配置类型获取配置
        /// </summary>
        /// <param name="configType">配置类型</param>
        /// <returns>配置列表</returns>
        [HttpGet("type/{configType}")]
        public async Task<Result<List<SystemConfigResponseDto>>> GetConfigsByType(string configType)
        {
            var configs = await _systemConfigService.GetConfigsByTypeAsync(configType);
            return Success(configs);
        }

        /// <summary>
        /// 批量更新配置
        /// </summary>
        /// <param name="configUpdates">配置更新字典</param>
        /// <returns>是否成功</returns>
        [HttpPost("batch-update")]
        public async Task<Result<bool>> BatchUpdateConfigs([FromBody] Dictionary<string, string> configUpdates)
        {
            var result = await _systemConfigService.BatchUpdateConfigsAsync(configUpdates, GetCurrentUserInfo());
            return Success(result, "批量更新配置成功");
        }

        /// <summary>
        /// 获取所有配置分组
        /// </summary>
        /// <returns>分组列表</returns>
        [HttpGet("groups")]
        public async Task<Result<List<string>>> GetConfigGroups()
        {
            var groups = await _systemConfigService.GetConfigGroupsAsync();
            return Success(groups);
        }

        /// <summary>
        /// 获取微信相关配置
        /// </summary>
        /// <returns>微信配置</returns>
        [HttpGet("wechat")]
        public async Task<Result<Dictionary<string, string>>> GetWechatConfigs()
        {
            var configs = await _systemConfigService.GetWechatConfigsAsync();
            return Success(configs);
        }

        /// <summary>
        /// 获取红包相关配置
        /// </summary>
        /// <returns>红包配置</returns>
        [HttpGet("reward")]
        public async Task<Result<Dictionary<string, string>>> GetRewardConfigs()
        {
            var configs = await _systemConfigService.GetRewardConfigsAsync();
            return Success(configs);
        }

        /// <summary>
        /// 获取系统相关配置
        /// </summary>
        /// <returns>系统配置</returns>
        [HttpGet("system")]
        public async Task<Result<Dictionary<string, string>>> GetSystemConfigs()
        {
            var configs = await _systemConfigService.GetSystemConfigsAsync();
            return Success(configs);
        }

        /// <summary>
        /// 启用/禁用配置
        /// </summary>
        /// <param name="configId">配置ID</param>
        /// <param name="isEnabled">是否启用</param>
        /// <returns>是否成功</returns>
        [HttpPost("{configId}/status")]
        public async Task<Result<bool>> UpdateConfigStatus(int configId, [FromBody] bool isEnabled)
        {
            var result = await _systemConfigService.UpdateConfigStatusAsync(configId, (byte)(isEnabled ? 1 : 0), GetCurrentUserInfo());
            return Success(result, isEnabled ? "配置已启用" : "配置已禁用");
        }
    }
}