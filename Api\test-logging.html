<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>日志功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
        }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            color: #fff;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .test-button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            color: white;
            border: none;
            padding: 12px 24px;
            margin: 10px 5px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px 0 rgba(31, 38, 135, 0.2);
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px 0 rgba(31, 38, 135, 0.4);
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 8px;
            background: rgba(0, 0, 0, 0.2);
            border-left: 4px solid #4ECDC4;
            white-space: pre-wrap;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left-color: #4ECDC4; }
        .error { border-left-color: #FF6B6B; }
        .info { border-left-color: #FFE66D; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 生产环境日志功能测试</h1>
        
        <div class="test-section">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试生产环境中所有API接口的日志记录功能。每个API调用都会在服务器端生成详细的日志记录。</p>
            <p><strong>日志文件位置：</strong></p>
            <ul>
                <li>错误日志：<code>logs/error/error_YYYY-MM-DD.log</code></li>
                <li>调试日志：<code>logs/debug/debug_YYYY-MM-DD.log</code></li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🔧 微信配置接口测试</h3>
            <button class="test-button" onclick="testWechatConfig()">📱 测试微信配置检查</button>
            <button class="test-button" onclick="testWechatAuthorize()">🔐 测试微信授权URL生成</button>
            <div id="wechat-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>👥 用户审核接口测试</h3>
            <button class="test-button" onclick="testUserAudit()">✅ 测试用户审核</button>
            <button class="test-button" onclick="testPendingUsers()">📋 测试待审核用户列表</button>
            <div id="audit-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>📊 用户行为接口测试</h3>
            <button class="test-button" onclick="testQuizSubmit()">📝 测试答题提交</button>
            <button class="test-button" onclick="testRewardGrant()">🧧 测试红包发放</button>
            <div id="behavior-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>🎥 视频管理接口测试</h3>
            <button class="test-button" onclick="testVideoList()">📹 测试视频列表</button>
            <button class="test-button" onclick="testBatchList()">📦 测试批次列表</button>
            <div id="video-result" class="result" style="display:none;"></div>
        </div>

        <div class="test-section">
            <h3>⚙️ 系统配置接口测试</h3>
            <button class="test-button" onclick="testSystemConfig()">🔧 测试系统配置</button>
            <div id="config-result" class="result" style="display:none;"></div>
        </div>
    </div>

    <script>
        const baseUrl = 'https://localhost:7048';
        const adminToken = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6InN1cGVyX2FkbWluIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvbmFtZSI6InN1cGVyX2FkbWluXzAwMSIsIlVzZXJUeXBlIjoiMSIsImh0dHA6Ly9zY2hlbWFzLm1pY3Jvc29mdC5jb20vd3MvMjAwOC8wNi9pZGVudGl0eS9jbGFpbXMvcm9sZSI6IkFkbWluIiwiZXhwIjoxNzU0MjQyMjEzLCJpc3MiOiJBZG1pblByb2plY3RUZW1wbGF0ZSIsImF1ZCI6IkFkbWluUHJvamVjdFRlbXBsYXRlIn0.ZLopWWmYtKXiisMBSck0DdnxSCCYon5MHPOvyxb20s4';

        async function apiRequest(url, options = {}) {
            try {
                const response = await fetch(url, {
                    ...options,
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    }
                });
                
                const result = await response.json();
                return {
                    status: response.status,
                    success: response.ok,
                    data: result
                };
            } catch (error) {
                return {
                    status: 0,
                    success: false,
                    error: error.message
                };
            }
        }

        function displayResult(elementId, result, testName) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            
            const timestamp = new Date().toLocaleString();
            let content = `[${timestamp}] ${testName}\n`;
            content += `状态码: ${result.status}\n`;
            content += `成功: ${result.success ? '✅' : '❌'}\n`;
            
            if (result.data) {
                content += `响应数据: ${JSON.stringify(result.data, null, 2)}\n`;
            }
            
            if (result.error) {
                content += `错误信息: ${result.error}\n`;
            }
            
            content += '\n' + '='.repeat(50) + '\n\n';
            
            element.textContent = content + element.textContent;
            element.className = `result ${result.success ? 'success' : 'error'}`;
        }

        // 微信配置接口测试
        async function testWechatConfig() {
            const result = await apiRequest(`${baseUrl}/api/Wechat/config-check`);
            displayResult('wechat-result', result, '微信配置检查');
        }

        async function testWechatAuthorize() {
            const result = await apiRequest(`${baseUrl}/api/Wechat/authorize?redirectUri=https://test.com&state=test123`);
            displayResult('wechat-result', result, '微信授权URL生成');
        }

        // 用户审核接口测试
        async function testUserAudit() {
            const result = await apiRequest(`${baseUrl}/api/UserAudit/audit-user/test_user_001`, {
                method: 'POST',
                headers: {
                    'Authorization': adminToken
                },
                body: JSON.stringify({
                    status: 1,
                    remark: '测试审核通过'
                })
            });
            displayResult('audit-result', result, '用户审核');
        }

        async function testPendingUsers() {
            const result = await apiRequest(`${baseUrl}/api/UserAudit/pending-users`, {
                headers: {
                    'Authorization': adminToken
                }
            });
            displayResult('audit-result', result, '待审核用户列表');
        }

        // 用户行为接口测试
        async function testQuizSubmit() {
            const result = await apiRequest(`${baseUrl}/api/UserBatchRecord/test_user_001/submit-answer`, {
                method: 'POST',
                body: JSON.stringify({
                    batchId: 1,
                    totalQuestions: 5,
                    correctAnswers: 4,
                    answerDetails: '{"q1":"A","q2":"B","q3":"C","q4":"D","q5":"A"}'
                })
            });
            displayResult('behavior-result', result, '答题提交');
        }

        async function testRewardGrant() {
            const result = await apiRequest(`${baseUrl}/api/UserBatchRecord/test_user_001/grant-reward`, {
                method: 'POST',
                body: JSON.stringify({
                    batchId: 1,
                    rewardAmount: 10.50,
                    transactionId: 'test_tx_' + Date.now(),
                    outTradeNo: 'test_trade_' + Date.now()
                })
            });
            displayResult('behavior-result', result, '红包发放');
        }

        // 视频管理接口测试
        async function testVideoList() {
            const result = await apiRequest(`${baseUrl}/api/Video/list?pageIndex=1&pageSize=10`, {
                headers: {
                    'Authorization': adminToken
                }
            });
            displayResult('video-result', result, '视频列表');
        }

        async function testBatchList() {
            const result = await apiRequest(`${baseUrl}/api/Batch/list?pageIndex=1&pageSize=10`, {
                headers: {
                    'Authorization': adminToken
                }
            });
            displayResult('video-result', result, '批次列表');
        }

        // 系统配置接口测试
        async function testSystemConfig() {
            const result = await apiRequest(`${baseUrl}/api/SystemConfig/get-config?key=AUTO_AUDIT_ENABLED`, {
                headers: {
                    'Authorization': adminToken
                }
            });
            displayResult('config-result', result, '系统配置');
        }

        // 页面加载完成后显示提示
        window.onload = function() {
            console.log('日志测试页面已加载，请点击按钮测试各个接口的日志记录功能');
        };
    </script>
</body>
</html>
