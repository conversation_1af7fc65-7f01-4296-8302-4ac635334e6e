
＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 14:59:46,594                                   
【执行时间】[95]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 15:01:28,738                                   
【执行时间】[102239]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.InvalidOperationException: A second operation was started on this context instance before a previous operation completed. This is usually caused by different threads concurrently using the same instance of DbContext. For more information on how to avoid threading issues with DbContext, see https://go.microsoft.com/fwlink/?linkid=2097913.
   at Microsoft.EntityFrameworkCore.Infrastructure.Internal.ConcurrencyDetector.EnterCriticalSection()
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(IList`1 entriesToSave, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.ChangeTracking.Internal.StateManager.SaveChangesAsync(StateManager stateManager, Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Pomelo.EntityFrameworkCore.MySql.Storage.Internal.MySqlExecutionStrategy.ExecuteAsync[TState,TResult](TState state, Func`4 operation, Func`4 verifySucceeded, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity) in D:\MyWork\works\hangzhou-service-video-sharing\Api\DAL\Databases\BaseQueryDLL.cs:line 112
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 15:23:44,322                                   
【执行时间】[113]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 15:23:44,322                                   
【执行时间】[113]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 15:54:52,976                                   
【执行时间】[115]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 15:54:52,976                                   
【执行时间】[115]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 16:07:39,824                                   
【执行时间】[100]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 16:07:39,824                                   
【执行时间】[100]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.Set[TEntity]()
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity)
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 16:07:40,816                                   
【执行时间】[1092]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.CheckDisposed()
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity) in D:\MyWork\works\hangzhou-service-video-sharing\Api\DAL\Databases\BaseQueryDLL.cs:line 112
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 16:07:41,861                                   
【执行时间】[2136]毫秒                                   
【错误位置】BLL.SysService.SysLogService 属性[(null)]                                   
【错误描述】日志写入失败                                   
【错误详情】
System.ObjectDisposedException: Cannot access a disposed context instance. A common cause of this error is disposing a context instance that was resolved from dependency injection and then later trying to use the same context instance elsewhere in your application. This may occur if you are calling 'Dispose' on the context instance, or wrapping it in a using statement. If you are using dependency injection, you should let the dependency injection container take care of disposing context instances.
Object name: 'MyContext'.
   at Microsoft.EntityFrameworkCore.DbContext.get_DbContextDependencies()
   at Microsoft.EntityFrameworkCore.DbContext.SaveChangesAsync(Boolean acceptAllChangesOnSuccess, CancellationToken cancellationToken)
   at DAL.Databases.BaseQueryDLL`2.AddAsync(TEntity entity) in D:\MyWork\works\hangzhou-service-video-sharing\Api\DAL\Databases\BaseQueryDLL.cs:line 112
   at BLL.SysService.SysLogService.<>c__DisplayClass3_0.<<Log>b__0>d.MoveNext() in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\SysService\SysLogService.cs:line 104

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:08:13,393                                   
【执行时间】[46104]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】=== WechatController.CheckConfig 开始 - ERROR测试 ===                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:08:13,407                                   
【执行时间】[46117]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】微信配置检查 - ERROR测试 - AppId: wx02bacea38f7f3ab5, AppSecret: 已设置(32位)                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:26:43,962                                   
【执行时间】[181301]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】微信OAuth获取access_token失败: {"errcode":40029,"errmsg":"invalid code, rid: 688f6393-27f91c4f-5ac2f485"}                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:26:44,005                                   
【执行时间】[181344]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】微信API错误详情 - 错误码: 40029, 错误信息: invalid code, rid: 688f6393-27f91c4f-5ac2f485                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:26:44,011                                   
【执行时间】[181349]毫秒                                   
【错误位置】Common.WX.WechatOAuthHelperLogger 属性[(null)]                                   
【错误描述】错误原因: code无效，可能已过期或已被使用                                   
【错误详情】

＝＝＝＝＝＝＝＝＝＝                                   
【日志级别】ERROR                                   
【记录时间】2025-08-03 21:26:44,012                                   
【执行时间】[181351]毫秒                                   
【错误位置】ServiceVideoSharing.Controllers.VideoControllers.WechatController 属性[(null)]                                   
【错误描述】微信OAuth登录业务异常: 获取微信access_token失败                                   
【错误详情】
Common.Exceptions.BusinessException: 获取微信access_token失败
   at BLL.VideoService.WechatOAuthService.HandleOAuthCallbackAsync(WechatOAuthCallbackDto callbackDto) in D:\MyWork\works\hangzhou-service-video-sharing\Api\BLL\VideoService\WechatOAuthService.cs:line 106
   at ServiceVideoSharing.Controllers.VideoControllers.WechatController.OAuthCallback(WechatOAuthCallbackDto callbackDto) in D:\MyWork\works\hangzhou-service-video-sharing\Api\ServiceVideoSharing\Controllers\VideoControllers\WechatController.cs:line 124
