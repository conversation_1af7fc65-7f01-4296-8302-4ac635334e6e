{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "BasisConfig": {"DataBaseConnectionStrings": {"MysqlConnectionString": "Server=127.0.0.1;User=root;Password=my@root;Database=servicevideosharing"}, "Redis": {"Instances": [{"InstanceName": "main", "Connection": "127.0.0.1:6380,password=123456", "DefaultDb": 0}, {"InstanceName": "game", "Connection": "************:63790,password=123456", "DefaultDb": 0}]}, "JWT": {"sign": "aVeryLongRandomStringThatIsAtLeast32Characters", "exp": 60000, "Issuer": "AdminProjectTemplate", "Audience": "AdminProjectTemplate"}, "RabbitMQ": {"HostName": "127.0.0.1", "Port": 5672, "UserName": "guest", "Password": "guest", "VirtualHost": "my_vhost", "MaxRetryCount": 3, "ConnectionPoolSize": 5}, "LogService": {"Path": "https://localhost:5001/CreateLogInfo"}, "Wx": {"AppId": "wx02bacea38f7f3ab5", "AppSecret": "9b6fb978f5b127feb592647e800e53f6"}}}