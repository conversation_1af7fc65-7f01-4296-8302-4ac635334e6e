using Newtonsoft.Json;

namespace Common.WX
{
    /// <summary>
    /// 微信用户信息DTO（从微信API获取）
    /// </summary>
    public class WechatUserInfoDto
    {
        /// <summary>
        /// 用户的唯一标识
        /// </summary>
        [JsonProperty("openid")]
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 用户昵称
        /// </summary>
        [JsonProperty("nickname")]
        public string Nickname { get; set; } = string.Empty;

        /// <summary>
        /// 用户的性别，值为1时是男性，值为2时是女性，值为0时是未知
        /// </summary>
        [JsonProperty("sex")]
        public int Sex { get; set; }

        /// <summary>
        /// 用户个人资料填写的省份
        /// </summary>
        [JsonProperty("province")]
        public string Province { get; set; } = string.Empty;

        /// <summary>
        /// 普通用户个人资料填写的城市
        /// </summary>
        [JsonProperty("city")]
        public string City { get; set; } = string.Empty;

        /// <summary>
        /// 国家，如中国为CN
        /// </summary>
        [JsonProperty("country")]
        public string Country { get; set; } = string.Empty;

        /// <summary>
        /// 用户头像，最后一个数值代表正方形头像大小（有0、46、64、96、132数值可选，0代表640*640正方形头像），用户没有头像时该项为空。若用户更换头像，原有头像URL将失效。
        /// </summary>
        [JsonProperty("headimgurl")]
        public string HeadImgUrl { get; set; } = string.Empty;

        /// <summary>
        /// 只有在用户将公众号绑定到微信开放平台帐号后，才会出现该字段。
        /// </summary>
        [JsonProperty("unionid")]
        public string? UnionId { get; set; }

        /// <summary>
        /// 用户特权信息，json 数组，如微信沃卡用户为（chinaunicom）
        /// </summary>
        public List<string>? Privilege { get; set; }
    }

    /// <summary>
    /// 微信Access Token响应DTO
    /// </summary>
    public class WechatAccessTokenResponseDto
    {
        /// <summary>
        /// 网页授权接口调用凭证,注意：此access_token与基础支持的access_token不同
        /// </summary>
        [JsonProperty("access_token")]
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// access_token接口调用凭证超时时间，单位（秒）
        /// </summary>
        [JsonProperty("expires_in")]
        public int ExpiresIn { get; set; }

        /// <summary>
        /// 用户刷新access_token
        /// </summary>
        [JsonProperty("refresh_token")]
        public string RefreshToken { get; set; } = string.Empty;

        /// <summary>
        /// 用户唯一标识
        /// </summary>
        [JsonProperty("openid")]
        public string OpenId { get; set; } = string.Empty;

        /// <summary>
        /// 用户授权的作用域，使用逗号（,）分隔
        /// </summary>
        [JsonProperty("scope")]
        public string Scope { get; set; } = string.Empty;

        /// <summary>
        /// 是否为快照页模式虚拟账号，只有当用户是快照页模式虚拟账号时返回，值为1
        /// </summary>
        public int? IsSnapshotUser { get; set; }

        /// <summary>
        /// 用户统一标识（针对一个微信开放平台帐号下的应用，同一用户的 unionid 是唯一的）
        /// </summary>
        public string? UnionId { get; set; }
    }

    /// <summary>
    /// 微信基础Access Token DTO
    /// </summary>
    public class WechatBasicAccessTokenDto
    {
        /// <summary>
        /// 获取到的凭证
        /// </summary>
        public string AccessToken { get; set; } = string.Empty;

        /// <summary>
        /// 凭证有效时间，单位：秒
        /// </summary>
        public int ExpiresIn { get; set; }
    }
}
