using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using Entity.Entitys.VideoEntity;

namespace BLL.Common
{
    /// <summary>
    /// 用户端JWT Token帮助类
    /// 专门用于微信用户的业务token，与管理系统token完全独立
    /// </summary>
    public static class UserJWTHelper
    {
        /// <summary>
        /// 用户端JWT密钥（与管理系统密钥独立）
        /// </summary>
        private static readonly string UserSecretKey = "UserVideoSharing2025!@#$%^&*()_+{}|:<>?[]\\;',./`~ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";

        /// <summary>
        /// 用户端Token有效期（7天）
        /// </summary>
        private static readonly TimeSpan UserTokenExpiry = TimeSpan.FromDays(7);

        /// <summary>
        /// 颁发者
        /// </summary>
        private static readonly string Issuer = "VideoSharingUserSystem";

        /// <summary>
        /// 受众
        /// </summary>
        private static readonly string Audience = "VideoSharingUsers";

        /// <summary>
        /// 为微信用户生成JWT Token
        /// </summary>
        /// <param name="user">用户信息</param>
        /// <returns>JWT Token</returns>
        public static string GenerateUserToken(User user)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));

            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.UTF8.GetBytes(UserSecretKey);

            var claims = new List<Claim>
            {
                new(ClaimTypes.NameIdentifier, user.Id),
                new(ClaimTypes.Name, user.Nickname ?? ""),
                new("OpenId", user.OpenId ?? ""),
                new("UnionId", user.UnionId ?? ""),
                new("AuditStatus", user.AuditStatus.ToString()),
                new("InviterId", user.InviterId ?? ""),
                new("TokenType", "UserBusiness"), // 标识为用户业务token
                new("CreateTime", user.CreateTime.ToString("yyyy-MM-dd HH:mm:ss"))
            };

            // 如果有员工ID，添加到claims
            if (!string.IsNullOrEmpty(user.EmployeeId))
            {
                claims.Add(new Claim("EmployeeId", user.EmployeeId));
            }

            // 如果有邀请时间，添加到claims
            if (user.InviteTime.HasValue)
            {
                claims.Add(new Claim("InviteTime", user.InviteTime.Value.ToString("yyyy-MM-dd HH:mm:ss")));
            }

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.Add(UserTokenExpiry),
                Issuer = Issuer,
                Audience = Audience,
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(key),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            return tokenHandler.WriteToken(token);
        }

        /// <summary>
        /// 验证用户端JWT Token
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>验证结果和Claims</returns>
        public static (bool IsValid, ClaimsPrincipal? Principal, string? ErrorMessage) ValidateUserToken(string token)
        {
            if (string.IsNullOrEmpty(token))
                return (false, null, "Token为空");

            try
            {
                var tokenHandler = new JwtSecurityTokenHandler();
                var key = Encoding.UTF8.GetBytes(UserSecretKey);

                var validationParameters = new TokenValidationParameters
                {
                    ValidateIssuerSigningKey = true,
                    IssuerSigningKey = new SymmetricSecurityKey(key),
                    ValidateIssuer = true,
                    ValidIssuer = Issuer,
                    ValidateAudience = true,
                    ValidAudience = Audience,
                    ValidateLifetime = true,
                    ClockSkew = TimeSpan.Zero // 不允许时间偏差
                };

                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

                // 验证token类型
                var tokenTypeClaim = principal.FindFirst("TokenType");
                if (tokenTypeClaim?.Value != "UserBusiness")
                {
                    return (false, null, "Token类型不正确");
                }

                return (true, principal, null);
            }
            catch (SecurityTokenExpiredException)
            {
                return (false, null, "Token已过期");
            }
            catch (SecurityTokenInvalidSignatureException)
            {
                return (false, null, "Token签名无效");
            }
            catch (SecurityTokenValidationException ex)
            {
                return (false, null, $"Token验证失败: {ex.Message}");
            }
            catch (Exception ex)
            {
                return (false, null, $"Token验证异常: {ex.Message}");
            }
        }

        /// <summary>
        /// 从Token中获取用户ID
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>用户ID</returns>
        public static string? GetUserIdFromToken(string token)
        {
            var (isValid, principal, _) = ValidateUserToken(token);
            if (!isValid || principal == null)
                return null;

            return principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
        }

        /// <summary>
        /// 从Token中获取用户审核状态
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>审核状态</returns>
        public static int? GetAuditStatusFromToken(string token)
        {
            var (isValid, principal, _) = ValidateUserToken(token);
            if (!isValid || principal == null)
                return null;

            var auditStatusClaim = principal.FindFirst("AuditStatus")?.Value;
            return int.TryParse(auditStatusClaim, out var status) ? status : null;
        }

        /// <summary>
        /// 从Token中获取邀请人ID
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>邀请人ID</returns>
        public static string? GetInviterIdFromToken(string token)
        {
            var (isValid, principal, _) = ValidateUserToken(token);
            if (!isValid || principal == null)
                return null;

            var inviterIdClaim = principal.FindFirst("InviterId")?.Value;
            return string.IsNullOrEmpty(inviterIdClaim) ? null : inviterIdClaim;
        }

        /// <summary>
        /// 检查Token是否即将过期（1天内）
        /// </summary>
        /// <param name="token">JWT Token</param>
        /// <returns>是否即将过期</returns>
        public static bool IsTokenExpiringSoon(string token)
        {
            var (isValid, principal, _) = ValidateUserToken(token);
            if (!isValid || principal == null)
                return true;

            var expClaim = principal.FindFirst(JwtRegisteredClaimNames.Exp)?.Value;
            if (long.TryParse(expClaim, out var exp))
            {
                var expTime = DateTimeOffset.FromUnixTimeSeconds(exp);
                return expTime.Subtract(DateTimeOffset.UtcNow) <= TimeSpan.FromDays(1);
            }

            return true;
        }
    }
}
