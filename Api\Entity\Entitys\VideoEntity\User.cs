using Microsoft.EntityFrameworkCore;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Entity.Entitys.VideoEntity
{
    /// <summary>
    /// 用户表 - 简化版，只保留核心用户信息
    /// </summary>
    [Table("users")]
    public class User : BaseEntity_GUID
    {
        /// <summary>
        /// 微信OpenID
        /// </summary>
        [MaxLength(100)]
        [Comment("微信OpenID")]
        public string? OpenId { get; set; }

        /// <summary>
        /// 微信UnionID
        /// </summary>
        [MaxLength(100)]
        [Comment("微信UnionID")]
        public string? UnionId { get; set; }

        /// <summary>
        /// 微信昵称
        /// </summary>
        [MaxLength(100)]
        [Comment("微信昵称")]
        public string? Nickname { get; set; }

        /// <summary>
        /// 头像URL
        /// </summary>
        [MaxLength(255)]
        [Comment("头像URL")]
        public string? Avatar { get; set; }

        /// <summary>
        /// 绑定的员工ID（关联SysUser表）
        /// </summary>
        [Comment("绑定的员工ID")]
        public string? EmployeeId { get; set; }

        /// <summary>
        /// 最后登录时间
        /// </summary>
        [Comment("最后登录时间")]
        public DateTime? LastLogin { get; set; }

        /// <summary>
        /// 审核状态：0=待审核，1=已通过，2=已拒绝
        /// </summary>
        [Comment("审核状态：0=待审核，1=已通过，2=已拒绝")]
        public int AuditStatus { get; set; } = 0;

        /// <summary>
        /// 邀请人ID（分享人的员工ID）
        /// </summary>
        [MaxLength(50)]
        [Comment("邀请人ID（分享人的员工ID）")]
        public string? InviterId { get; set; }

        /// <summary>
        /// 邀请时间
        /// </summary>
        [Comment("邀请时间")]
        public DateTime? InviteTime { get; set; }
    }
}
