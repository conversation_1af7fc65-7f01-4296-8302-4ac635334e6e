using Autofac;
using Autofac.Extensions.DependencyInjection;
using Common;
using Common.Helper;
using Common.RabbitMQHelper.Entity;
using Common.Redis;
using DAL;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Http.Features;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Pomelo.EntityFrameworkCore.MySql;
using ServiceVideoSharing.Controllers.Filter;
using ServiceVideoSharing.Controllers.Middleware;
using ServiceVideoSharing.Infrastructure;
using System.Reflection;
using System.Text;
using FFMpegCore;
using log4net;
using log4net.Config;

var builder = WebApplication.CreateBuilder(args);

// 配置log4net
var log4netConfigFile = Path.Combine(AppContext.BaseDirectory, "Config", "log4net.config");
if (File.Exists(log4netConfigFile))
{
    XmlConfigurator.Configure(new FileInfo(log4netConfigFile));
    Console.WriteLine($"log4net配置文件已加载: {log4netConfigFile}");
}
else
{
    Console.WriteLine($"log4net配置文件未找到: {log4netConfigFile}");
}

// 配置文件热更新支持
builder.Configuration
    .SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    //.AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables();

// 注册配置监控服务
builder.Services.AddSingleton<ConfigurationMonitor>();

// 加载基础配置并初始化静态属性
var basisConfig = builder.Configuration.GetSection("BasisConfig").Get<BasisConfigDto>();
if (basisConfig != null)
{
    // 初始化微信配置
    WxSetting.AppId = builder.Configuration.GetSection("BasisConfig:Wx:AppId").Value ?? "";
    WxSetting.AppSecret = builder.Configuration.GetSection("BasisConfig:Wx:AppSecret").Value ?? "";

    // 初始化JWT配置
    JWTSetting.Sign = builder.Configuration.GetSection("BasisConfig:Jwt:Sign").Value ?? "";
    if (int.TryParse(builder.Configuration.GetSection("BasisConfig:Jwt:Exp").Value, out int exp))
    {
        JWTSetting.Exp = exp;
    }
    JWTSetting.Issuer = builder.Configuration.GetSection("BasisConfig:Jwt:Issuer").Value ?? "";
    JWTSetting.Audience = builder.Configuration.GetSection("BasisConfig:Jwt:Audience").Value ?? "";

    // 初始化Redis配置
    var redisInstances = builder.Configuration.GetSection("BasisConfig:Redis:Instances").Get<List<RedisInstance>>();
    if (redisInstances != null)
    {
        RedisSetting.Instances.Clear();
        RedisSetting.Instances.AddRange(redisInstances);
    }

    // 初始化数据库连接字符串
    DataBaseConnectionStrings.MysqlConnectionString = builder.Configuration.GetSection("BasisConfig:DataBaseConnectionStrings:MysqlConnectionString").Value ?? "";

    // 初始化日志服务配置
    LogServiceSetting.Path = builder.Configuration.GetSection("BasisConfig:LogService:Path").Value ?? "";

    // 初始化CardpoenApi配置
    CardpoenApiSetting.OpenApiKey = builder.Configuration.GetSection("BasisConfig:CardpoenApi:OpenApiKey").Value ?? "";
}

// 配置RabbitMQ和Redis选项
builder.Services.Configure<RabbitMQOptions>(builder.Configuration.GetSection("BasisConfig:RabbitMQ"));
builder.Services.Configure<RedisOptions>(builder.Configuration.GetSection("BasisConfig:Redis"));

// 注册HttpClient工厂
builder.Services.AddHttpClient();

// 添加JWT身份验证
builder.Services.AddAuthentication(options =>
{
    options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
    options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
})
.AddJwtBearer(options =>
{
    options.TokenValidationParameters = new TokenValidationParameters
    {
        ValidateIssuerSigningKey = true,
        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(JWTSetting.Sign)),
        ValidateIssuer = true,
        ValidIssuer = JWTSetting.Issuer,
        ValidateAudience = true,
        ValidAudience = JWTSetting.Audience,
        ValidateLifetime = true,
        ClockSkew = TimeSpan.Zero
    };
});

// 添加日志服务
builder.Services.AddLogging();

// 注册HttpContextAccessor（这个是框架服务，不会被Autofac自动注册）
builder.Services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();

// 注册DbContext（使用标准EF Core注册方式避免生命周期问题）
builder.Services.AddDbContext<MyContext>(options =>
{
    var connectionString = builder.Configuration.GetSection("BasisConfig:DataBaseConnectionStrings:MysqlConnectionString").Value
        ?? throw new InvalidOperationException("MySQL connection string not found.");

    var serverVersion = ServerVersion.AutoDetect(connectionString);
    options.UseMySql(connectionString, serverVersion)
        .EnableSensitiveDataLogging()
        .EnableDetailedErrors();
});







// 使用Autofac作为依赖注入容器
// 所有以Service、Repository结尾的类以及带有[Dependency]特性的类都会被自动注册
builder.Host.UseServiceProviderFactory(new AutofacServiceProviderFactory())
    .ConfigureContainer<ContainerBuilder>(containerBuilder =>
    {
        containerBuilder.RegisterModule(new AutofacModule());
    });

// 配置Kestrel
builder.WebHost.ConfigureKestrel(serverOptions =>
{
    serverOptions.Limits.MaxRequestBodySize = **********; // 2GB = 2 * 1024 * 1024 * 1024 bytes
});

// 配置临时文件夹
var tempFolder = Path.Combine(Environment.CurrentDirectory, "temp");
if (!Directory.Exists(tempFolder))
{
    Directory.CreateDirectory(tempFolder);
}

// 配置FFMpegCore使用项目内的FFmpeg工具
var ffmpegPath = Path.Combine(Environment.CurrentDirectory, "FFmpeg");
if (Directory.Exists(ffmpegPath))
{
    GlobalFFOptions.Configure(new FFOptions { BinaryFolder = ffmpegPath });
}

// 配置表单选项以支持大文件上传
builder.Services.Configure<FormOptions>(options =>
{
    options.ValueLengthLimit = int.MaxValue;
    options.MultipartBodyLengthLimit = **********; // 2GB
    options.MultipartHeadersLengthLimit = int.MaxValue;
});

builder.Services.AddControllers();
builder.Services.AddEndpointsApiExplorer();

// 配置CORS
builder.Services.AddCors(options =>
{
    options.AddPolicy("myCors", policy =>
    {
        policy.SetIsOriginAllowed(_ => true) // 允许任何来源
              .AllowAnyHeader()
              .AllowAnyMethod()
              .DisallowCredentials(); // 禁用 credentials，这样就可以使用通配符源
    });
});

// 配置Swagger
builder.Services.AddSwaggerGen(options =>
{
    options.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
    {
        Description = "在下框中输入请求头中需要添加Jwt授权Token：Bearer Token",
        Name = "Authorization",
        In = ParameterLocation.Header,
        Type = SecuritySchemeType.ApiKey,
        BearerFormat = "JWT",
        Scheme = "Bearer"
    });
    options.AddSecurityRequirement(new OpenApiSecurityRequirement
    {
        {
            new OpenApiSecurityScheme
            {
                Reference = new OpenApiReference {
                    Type = ReferenceType.SecurityScheme,
                    Id = "Bearer"
                }
            },
            Array.Empty<string>()
        }
    });





    // 自定义 Schema ID 生成器，解决泛型类型冲突
    options.CustomSchemaIds(type =>
    {
        return GetTypeDisplayName(type);

        static string GetTypeDisplayName(Type t)
        {
            if (t.IsGenericType)
            {
                var name = t.GetGenericTypeDefinition().Name;
                // 移除泛型参数标记
                name = System.Text.RegularExpressions.Regex.Replace(name, @"`\d+", "");
                var args = string.Join("", t.GetGenericArguments().Select(GetTypeDisplayName));
                return $"{name}Of{args}";
            }

            // 对于非泛型类型，如果有命名空间冲突，包含部分命名空间信息
            if (t.FullName != null && t.FullName.Contains("VideoDto"))
            {
                return $"Video{t.Name}";
            }
            else if (t.FullName != null && t.FullName.Contains("SysDto"))
            {
                return $"Sys{t.Name}";
            }

            return t.Name;
        }
    });
    // 加载XML注释文件
    var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
    var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);
    if (File.Exists(xmlPath))
    {
        options.IncludeXmlComments(xmlPath);
    }
});

// 添加控制器和视图
builder.Services.AddControllersWithViews();

var app = builder.Build();

app.UseSwagger();
app.UseSwaggerUI();

// 添加全局异常处理中间件
app.UseExceptionMiddleware();

// 将CORS中间件移到管道的早期位置
app.UseCors("myCors");

app.UseHttpsRedirection();

// 配置默认静态文件服务（wwwroot目录）
app.UseStaticFiles();

// 配置额外的静态文件服务，提供视频文件访问
var uploadPath = Path.Combine(Environment.CurrentDirectory, "UploadFile");
if (!Directory.Exists(uploadPath))
{
    Directory.CreateDirectory(uploadPath);
}

app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(uploadPath),
    RequestPath = ""
});

// 配置wwwroot目录的静态文件服务（用于访问上传的文件）
var wwwrootPath = Path.Combine(Environment.CurrentDirectory, "wwwroot");
if (!Directory.Exists(wwwrootPath))
{
    Directory.CreateDirectory(wwwrootPath);
}

app.UseStaticFiles(new StaticFileOptions
{
    FileProvider = new PhysicalFileProvider(wwwrootPath),
    RequestPath = "/wwwroot"
});

app.UseAuthentication();
app.UseAuthorization();

// 添加权限验证中间件（在授权后，控制器前）
app.UsePermissionValidation();

// 添加缓存中间件（在权限验证后，控制器前）
app.UseCaching();

app.MapControllers();

app.Run();
