using BLL.VideoService;
using Common.Log4Net;
using Entity.Dto.VideoDto;
using Microsoft.AspNetCore.Mvc;
using ServiceVideoSharing.Controllers.Attributes;
using ServiceVideoSharing.Controllers.BasisController;
using ServiceVideoSharing.Controllers.BasisController.ResuItEntity;

namespace ServiceVideoSharing.Controllers.VideoControllers
{
    /// <summary>
    /// 用户审核控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    [Permission]
    public class UserAuditController(UserAuditService userAuditService, UserService userService) : BaseController
    {
        private readonly UserAuditService _userAuditService = userAuditService;
        private readonly UserService _userService = userService;

        /// <summary>
        /// 员工审核用户
        /// </summary>
        /// <param name="userId">用户ID</param>
        /// <param name="auditDto">审核信息</param>
        /// <returns>是否成功</returns>
        [HttpPost("audit-user/{userId}", Name = "UserAudit_AuditUser")]
        public async Task<Result<bool>> AuditUser(string userId, [FromBody] UserAuditDto auditDto)
        {
            await LoggerHelper<UserAuditController>.InfoAsync("=== 员工审核用户接口开始 ===");
            await LoggerHelper<UserAuditController>.InfoAsync($"接收参数 - 用户ID: {userId}, 审核状态: {auditDto.Status}, 备注: {auditDto.Remark}");

            try
            {
                // 获取当前登录的员工信息
                var currentUserId = GetCurrentUserId();
                await LoggerHelper<UserAuditController>.InfoAsync($"当前审核员工ID: {currentUserId}");

                // 员工审核用户，使用当前登录员工的ID作为审核员ID
                await LoggerHelper<UserAuditController>.InfoAsync("开始调用UserAuditService进行审核");
                var result = await _userAuditService.AuditUserByEmployeeAsync(
                    userId,
                    currentUserId,
                    auditDto.Status,
                    auditDto.Remark,
                    GetCurrentUserInfo());

                var statusText = auditDto.Status == 1 ? "审核通过" : "审核拒绝";
                await LoggerHelper<UserAuditController>.InfoAsync($"审核完成 - 结果: {result}, 状态: {statusText}");
                await LoggerHelper<UserAuditController>.InfoAsync("=== 员工审核用户接口成功完成 ===");

                return Success(result, statusText);
            }
            catch (Exception ex)
            {
                await LoggerHelper<UserAuditController>.ErrorAsync($"员工审核用户异常 - 用户ID: {userId}, 审核状态: {auditDto.Status}, 错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取当前员工的待审核用户列表
        /// </summary>
        /// <returns>待审核用户列表</returns>
        [HttpGet("pending-users", Name = "UserAudit_GetPendingUsers")]
        public async Task<Result<List<UserResponseDto>>> GetPendingUsers()
        {
            await LoggerHelper<UserAuditController>.InfoAsync("=== 获取员工待审核用户列表接口开始 ===");

            try
            {
                // 获取当前登录的员工信息
                var currentUserId = GetCurrentUserId();
                await LoggerHelper<UserAuditController>.InfoAsync($"当前员工ID: {currentUserId}");

                // 只获取属于当前员工的待审核用户
                await LoggerHelper<UserAuditController>.InfoAsync("开始查询员工的待审核用户");
                var result = await _userService.GetPendingAuditUsersByEmployeeAsync(currentUserId);

                await LoggerHelper<UserAuditController>.InfoAsync($"查询完成 - 找到 {result.Count} 个待审核用户");
                await LoggerHelper<UserAuditController>.InfoAsync("=== 获取员工待审核用户列表接口成功完成 ===");

                return Success(result);
            }
            catch (Exception ex)
            {
                await LoggerHelper<UserAuditController>.ErrorAsync($"获取员工待审核用户列表异常 - 错误: {ex.Message}", ex);
                throw;
            }
        }

        /// <summary>
        /// 获取所有待审核用户列表（管理员权限）
        /// </summary>
        /// <returns>所有待审核用户列表</returns>
        [HttpGet("all-pending-users", Name = "UserAudit_GetAllPendingUsers")]
        public async Task<Result<List<UserResponseDto>>> GetAllPendingUsers()
        {
            await LoggerHelper<UserAuditController>.InfoAsync("=== 获取所有待审核用户列表接口开始 ===");

            try
            {
                // 获取所有待审核用户
                await LoggerHelper<UserAuditController>.InfoAsync("开始查询所有待审核用户");
                var result = await _userService.GetAllPendingAuditUsersAsync();

                await LoggerHelper<UserAuditController>.InfoAsync($"查询完成 - 找到 {result.Count} 个待审核用户");
                await LoggerHelper<UserAuditController>.InfoAsync("=== 获取所有待审核用户列表接口成功完成 ===");

                return Success(result, $"获取到 {result.Count} 个待审核用户");
            }
            catch (Exception ex)
            {
                await LoggerHelper<UserAuditController>.ErrorAsync($"获取所有待审核用户列表异常 - 错误: {ex.Message}", ex);
                throw;
            }
        }

    }
}
